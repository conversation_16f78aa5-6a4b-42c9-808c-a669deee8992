#!/bin/bash

set -e  # 遇到錯誤時停止執行

# 顏色定義
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日誌函數
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 設置部署 ID（使用時間戳）
TIMESTAMP=$(date +%Y%m%d%H%M%S)
export DEPLOYMENT_GROUP_ID=${DEPLOYMENT_GROUP_ID:-"local-deployment-group"}
export DEPLOYMENT_ID="d-${TIMESTAMP}-local"
export APPLICATION_NAME=${APPLICATION_NAME:-"kktv-drm"}
export INSTANCE_ENV=${INSTANCE_ENV:-"test"}

# 設置部署目錄結構（模擬 AWS CodeDeploy 的目錄結構）
DEPLOY_ROOT=/opt/codedeploy-agent/deployment-root
DEPLOY_DIR=$DEPLOY_ROOT/$DEPLOYMENT_GROUP_ID/$DEPLOYMENT_ID
DEPLOY_ARCHIVE=$DEPLOY_DIR/deployment-archive
DEPLOY_LOGS=$DEPLOY_DIR/logs

log_info "開始模擬 AWS CodeDeploy 部署流程..."
log_info "部署 ID: $DEPLOYMENT_ID"
log_info "部署組: $DEPLOYMENT_GROUP_ID"
log_info "應用名稱: $APPLICATION_NAME"
log_info "環境: $INSTANCE_ENV"

# 清理之前的部署（可選）
if [ "$1" = "--clean" ]; then
    log_warning "清理之前的部署..."
    rm -rf $DEPLOY_ROOT/*
fi

# 創建部署目錄結構
log_info "創建部署目錄結構..."
mkdir -p $DEPLOY_ARCHIVE
mkdir -p $DEPLOY_LOGS

# 複製應用代碼到部署目錄
log_info "複製應用代碼到部署目錄..."
cp -r /app/* $DEPLOY_ARCHIVE/

# 創建日誌文件
touch $DEPLOY_LOGS/scripts.log

# 記錄部署開始
echo "$(date '+%Y-%m-%d %H:%M:%S') [INFO] Deployment started - ID: $DEPLOYMENT_ID" >> $DEPLOY_LOGS/scripts.log

# 執行 ApplicationStop 階段（如果存在舊部署）
if [ -f "$DEPLOY_ARCHIVE/codedeploy/application-stop.sh" ]; then
    log_info "執行 ApplicationStop 階段..."
    echo "$(date '+%Y-%m-%d %H:%M:%S') LifecycleEvent - ApplicationStop" >> $DEPLOY_LOGS/scripts.log
    cd $DEPLOY_ARCHIVE
    chmod +x ./codedeploy/application-stop.sh
    if ./codedeploy/application-stop.sh >> $DEPLOY_LOGS/scripts.log 2>&1; then
        log_success "ApplicationStop 階段完成"
    else
        log_warning "ApplicationStop 階段失敗（可能是首次部署）"
    fi
fi

# 執行 BeforeInstall 階段
log_info "執行 BeforeInstall 階段 (ansible.sh)..."
echo "$(date '+%Y-%m-%d %H:%M:%S') LifecycleEvent - BeforeInstall" >> $DEPLOY_LOGS/scripts.log
cd $DEPLOY_ARCHIVE
chmod +x ./codedeploy/ansible.sh

if ./codedeploy/ansible.sh >> $DEPLOY_LOGS/scripts.log 2>&1; then
    log_success "BeforeInstall 階段完成"
else
    log_error "BeforeInstall 階段失敗!"
    echo "查看日誌: $DEPLOY_LOGS/scripts.log"
    exit 1
fi

# 執行 AfterInstall 階段
log_info "執行 AfterInstall 階段 (install.sh)..."
echo "$(date '+%Y-%m-%d %H:%M:%S') LifecycleEvent - AfterInstall" >> $DEPLOY_LOGS/scripts.log
chmod +x ./codedeploy/install.sh

if ./codedeploy/install.sh >> $DEPLOY_LOGS/scripts.log 2>&1; then
    log_success "AfterInstall 階段完成"
else
    log_error "AfterInstall 階段失敗!"
    echo "查看日誌: $DEPLOY_LOGS/scripts.log"
    exit 1
fi

# 執行 ApplicationStart 階段
log_info "執行 ApplicationStart 階段 (restart.sh)..."
echo "$(date '+%Y-%m-%d %H:%M:%S') LifecycleEvent - ApplicationStart" >> $DEPLOY_LOGS/scripts.log
chmod +x ./codedeploy/restart.sh

if ./codedeploy/restart.sh >> $DEPLOY_LOGS/scripts.log 2>&1; then
    log_success "ApplicationStart 階段完成"
else
    log_error "ApplicationStart 階段失敗!"
    echo "查看日誌: $DEPLOY_LOGS/scripts.log"
    exit 1
fi

# Docker 環境特殊處理：修復 C++ 模組編譯和服務啟動問題
log_info "執行 Docker 環境特殊修復..."
echo "$(date '+%Y-%m-%d %H:%M:%S') Docker Fix - Rebuilding C++ modules and starting services" >> $DEPLOY_LOGS/scripts.log

cd $DEPLOY_ARCHIVE

# 修復 C++ 模組編譯問題
log_info "重新編譯 C++ 模組..."
if [ -d "drm-plugin-fairplay" ]; then
    cd drm-plugin-fairplay
    if npm run build >> $DEPLOY_LOGS/scripts.log 2>&1 || node-gyp rebuild >> $DEPLOY_LOGS/scripts.log 2>&1; then
        log_success "fairplay 模組編譯成功"
    else
        log_warning "fairplay 模組編譯失敗"
    fi
    cd ..
fi

if [ -d "drm-plugin-playready" ]; then
    cd drm-plugin-playready
    if npm run build >> $DEPLOY_LOGS/scripts.log 2>&1 || node-gyp rebuild >> $DEPLOY_LOGS/scripts.log 2>&1; then
        log_success "playready 模組編譯成功"
    else
        log_warning "playready 模組編譯失敗"
    fi
    cd ..
fi

if [ -d "drm-plugin-widevine" ]; then
    cd drm-plugin-widevine
    if npm run build >> $DEPLOY_LOGS/scripts.log 2>&1 || node-gyp rebuild >> $DEPLOY_LOGS/scripts.log 2>&1; then
        log_success "widevine 模組編譯成功"
    else
        log_warning "widevine 模組編譯失敗"
    fi
    cd ..
fi

# 手動啟動服務（因為 upstart 在 Docker 中不工作）
log_info "手動啟動 DRM 服務..."
cd /kkcorp/kktv-drm

# 確保環境變數設置
export INSTANCE_ENV=${INSTANCE_ENV:-test}

# 檢查服務是否已在運行
if pgrep -f "node drm_server.js" > /dev/null; then
    log_info "停止現有的 DRM 服務..."
    pkill -f "node drm_server.js"
    sleep 2
fi

# 啟動服務
log_info "啟動 DRM 服務..."
nohup node drm_server.js -p /var/run/kktv-drm.pid > /var/log/kktv-drm.log 2>&1 &
DRMS_PID=$!

# 等待服務啟動
sleep 3

# 檢查服務是否成功啟動
if kill -0 $DRMS_PID 2>/dev/null; then
    log_success "DRM 服務啟動成功 (PID: $DRMS_PID)"
    echo "$(date '+%Y-%m-%d %H:%M:%S') DRM Service started successfully with PID: $DRMS_PID" >> $DEPLOY_LOGS/scripts.log
else
    log_error "DRM 服務啟動失敗"
    echo "$(date '+%Y-%m-%d %H:%M:%S') DRM Service failed to start" >> $DEPLOY_LOGS/scripts.log
    echo "查看錯誤日誌: tail -f /var/log/kktv-drm.log"
fi

# 記錄部署完成
echo "$(date '+%Y-%m-%d %H:%M:%S') [INFO] Deployment completed successfully - ID: $DEPLOYMENT_ID" >> $DEPLOY_LOGS/scripts.log

# 部署成功總結
log_success "部署模擬完成!"
echo ""
echo "部署資訊:"
echo "  應用已部署到: $DEPLOY_ARCHIVE"
echo "  部署日誌: $DEPLOY_LOGS/scripts.log"
echo "  符號連結: /kkcorp/kktv-drm -> $DEPLOY_ARCHIVE"
echo ""

# 顯示符號連結狀態
if [ -L "/kkcorp/kktv-drm" ]; then
    LINK_TARGET=$(readlink /kkcorp/kktv-drm)
    log_success "符號連結已建立: /kkcorp/kktv-drm -> $LINK_TARGET"
else
    log_warning "符號連結不存在或建立失敗"
fi

# 檢查服務狀態（如果有的話）
log_info "檢查應用狀態..."
if [ -f "/kkcorp/kktv-drm/server.js" ]; then
    log_success "應用文件存在"
    # 可以在這裡添加更多的健康檢查
else
    log_warning "應用文件不存在"
fi

# 提供有用的命令
echo ""
echo "有用的命令:"
echo "  查看完整日誌: cat $DEPLOY_LOGS/scripts.log"
echo "  進入部署目錄: cd $DEPLOY_ARCHIVE"
echo "  檢查符號連結: ls -la /kkcorp/"
echo "  測試應用: cd /kkcorp/kktv-drm && node server.js"
echo ""

log_success "部署模擬腳本執行完成!"