#!/bin/bash

# 本地 CodeDeploy 測試環境設置腳本

set -e

# 顏色定義
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 檢查 Docker 是否安裝
check_docker() {
    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安裝，請先安裝 Docker"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose 未安裝，請先安裝 Docker Compose"
        exit 1
    fi
    
    log_success "Docker 環境檢查通過"
}

# 設置文件權限
setup_permissions() {
    log_info "設置文件權限..."
    
    # 確保腳本有執行權限
    chmod +x simulate-codedeploy.sh
    chmod +x codedeploy/*.sh 2>/dev/null || log_warning "codedeploy 目錄中的腳本權限設置可能失敗"
    chmod +x scripts/*.sh 2>/dev/null || true
    
    log_success "文件權限設置完成"
}

# 構建 Docker 映像
build_image() {
    log_info "構建 Docker 映像..."
    
    if docker-compose build; then
        log_success "Docker 映像構建成功"
    else
        log_error "Docker 映像構建失敗"
        exit 1
    fi
}

# 啟動容器
start_container() {
    log_info "啟動 Docker 容器..."
    
    if docker-compose up -d; then
        log_success "Docker 容器啟動成功"
        
        # 等待容器完全啟動
        sleep 5
        
        # 檢查容器狀態
        if docker-compose ps | grep -q "Up"; then
            log_success "容器運行狀態正常"
        else
            log_warning "容器可能未正常啟動，請檢查"
        fi
    else
        log_error "Docker 容器啟動失敗"
        exit 1
    fi
}

# 顯示使用說明
show_usage() {
    echo ""
    log_success "設置完成！"
    echo ""
    echo "接下來的操作："
    echo ""
    echo "1. 進入容器："
    echo "   docker-compose exec codedeploy-sim bash"
    echo ""
    echo "2. 執行部署模擬："
    echo "   ./simulate-codedeploy.sh"
    echo ""
    echo "3. 查看容器狀態："
    echo "   docker-compose ps"
    echo ""
    echo "4. 查看容器日誌："
    echo "   docker-compose logs -f codedeploy-sim"
    echo ""
    echo "5. 停止容器："
    echo "   docker-compose down"
    echo ""
    echo "或者使用便捷腳本："
    echo "   ./scripts/deploy.sh     # 執行部署"
    echo "   ./scripts/logs.sh       # 查看日誌"
    echo "   ./scripts/cleanup.sh    # 清理環境"
    echo ""
}

# 主函數
main() {
    log_info "開始設置本地 CodeDeploy 測試環境..."
    
    check_docker
    setup_permissions
    build_image
    start_container
    show_usage
    
    log_success "環境設置完成！"
}

# 執行主函數
main "$@"