#!/bin/bash

# 環境測試腳本

set -e

# 顏色定義
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[✓]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[⚠]${NC} $1"
}

log_error() {
    echo -e "${RED}[✗]${NC} $1"
}

# 測試計數器
TESTS_TOTAL=0
TESTS_PASSED=0
TESTS_FAILED=0

# 測試函數
run_test() {
    local test_name="$1"
    local test_command="$2"
    
    TESTS_TOTAL=$((TESTS_TOTAL + 1))
    
    echo -n "測試 $TESTS_TOTAL: $test_name ... "
    
    if eval "$test_command" >/dev/null 2>&1; then
        echo -e "${GREEN}通過${NC}"
        TESTS_PASSED=$((TESTS_PASSED + 1))
        return 0
    else
        echo -e "${RED}失敗${NC}"
        TESTS_FAILED=$((TESTS_FAILED + 1))
        return 1
    fi
}

# 測試 Docker 環境
test_docker_environment() {
    log_info "測試 Docker 環境..."
    
    run_test "Docker 已安裝" "command -v docker"
    run_test "Docker Compose 已安裝" "command -v docker-compose"
    run_test "Docker 服務運行中" "docker info"
}

# 測試文件結構
test_file_structure() {
    log_info "測試文件結構..."
    
    run_test "Dockerfile 存在" "[ -f Dockerfile ]"
    run_test "docker-compose.yml 存在" "[ -f docker-compose.yml ]"
    run_test "simulate-codedeploy.sh 存在" "[ -f simulate-codedeploy.sh ]"
    run_test "appspec.yml 存在" "[ -f appspec.yml ]"
    run_test "codedeploy 目錄存在" "[ -d codedeploy ]"
    run_test "ansible.sh 存在" "[ -f codedeploy/ansible.sh ]"
    run_test "install.sh 存在" "[ -f codedeploy/install.sh ]"
    run_test "restart.sh 存在" "[ -f codedeploy/restart.sh ]"
}

# 測試文件權限
test_file_permissions() {
    log_info "測試文件權限..."
    
    run_test "simulate-codedeploy.sh 可執行" "[ -x simulate-codedeploy.sh ]"
    run_test "setup.sh 可執行" "[ -x scripts/setup.sh ]"
    run_test "deploy.sh 可執行" "[ -x scripts/deploy.sh ]"
    run_test "logs.sh 可執行" "[ -x scripts/logs.sh ]"
    run_test "cleanup.sh 可執行" "[ -x scripts/cleanup.sh ]"
}

# 測試容器構建
test_container_build() {
    log_info "測試容器構建..."
    
    run_test "Docker 映像構建" "docker-compose build --quiet"
}

# 測試容器啟動
test_container_startup() {
    log_info "測試容器啟動..."
    
    # 停止現有容器
    docker-compose down >/dev/null 2>&1 || true
    
    run_test "容器啟動" "docker-compose up -d"
    
    # 等待容器完全啟動
    sleep 5
    
    run_test "容器運行狀態" "docker-compose ps | grep -q Up"
}

# 測試部署模擬
test_deployment_simulation() {
    log_info "測試部署模擬..."
    
    # 確保容器在運行
    if ! docker-compose ps | grep -q "Up"; then
        log_error "容器未運行，跳過部署測試"
        return 1
    fi
    
    run_test "執行部署模擬" "timeout 300 docker-compose exec -T codedeploy-sim ./simulate-codedeploy.sh"
    
    # 檢查部署結果
    run_test "符號連結建立" "docker-compose exec -T codedeploy-sim [ -L /kkcorp/kktv-drm ]"
    run_test "部署目錄存在" "docker-compose exec -T codedeploy-sim [ -d /opt/codedeploy-agent/deployment-root ]"
    run_test "部署日誌存在" "docker-compose exec -T codedeploy-sim find /opt/codedeploy-agent/deployment-root -name 'scripts.log' | grep -q scripts.log"
}

# 測試應用功能
test_application_functionality() {
    log_info "測試應用功能..."
    
    if ! docker-compose ps | grep -q "Up"; then
        log_error "容器未運行，跳過應用測試"
        return 1
    fi
    
    run_test "應用文件存在" "docker-compose exec -T codedeploy-sim [ -f /kkcorp/kktv-drm/server.js ]"
    run_test "package.json 存在" "docker-compose exec -T codedeploy-sim [ -f /kkcorp/kktv-drm/package.json ]"
    run_test "node_modules 存在" "docker-compose exec -T codedeploy-sim [ -d /kkcorp/kktv-drm/node_modules ]"
}

# 測試輔助腳本
test_helper_scripts() {
    log_info "測試輔助腳本..."
    
    run_test "logs.sh 腳本功能" "./scripts/logs.sh --status >/dev/null"
    run_test "cleanup.sh 腳本功能" "./scripts/cleanup.sh --help >/dev/null"
}

# 清理測試環境
cleanup_test_environment() {
    log_info "清理測試環境..."
    
    docker-compose down >/dev/null 2>&1 || true
    log_success "測試環境已清理"
}

# 顯示測試結果摘要
show_test_summary() {
    echo ""
    echo "=== 測試結果摘要 ==="
    echo "總測試數: $TESTS_TOTAL"
    echo -e "通過: ${GREEN}$TESTS_PASSED${NC}"
    echo -e "失敗: ${RED}$TESTS_FAILED${NC}"
    
    if [ $TESTS_FAILED -eq 0 ]; then
        echo ""
        log_success "所有測試通過！環境設置正確。"
        echo ""
        echo "您現在可以："
        echo "  1. 執行部署: ./scripts/deploy.sh"
        echo "  2. 查看日誌: ./scripts/logs.sh"
        echo "  3. 清理環境: ./scripts/cleanup.sh"
        return 0
    else
        echo ""
        log_error "有 $TESTS_FAILED 個測試失敗。請檢查環境設置。"
        echo ""
        echo "常見問題排解："
        echo "  1. 確保 Docker 和 Docker Compose 已正確安裝"
        echo "  2. 確保有足夠的磁盤空間"
        echo "  3. 檢查文件權限設置"
        echo "  4. 查看 Docker 日誌: docker-compose logs"
        return 1
    fi
}

# 顯示使用說明
show_help() {
    echo "環境測試腳本"
    echo ""
    echo "使用方法："
    echo "  $0           # 執行完整測試"
    echo "  $0 --quick   # 執行快速測試（跳過部署）"
    echo "  $0 --deploy  # 僅測試部署功能"
    echo "  $0 --help    # 顯示此說明"
}

# 主函數
main() {
    case "$1" in
        --quick)
            log_info "執行快速測試..."
            test_docker_environment
            test_file_structure
            test_file_permissions
            test_container_build
            test_container_startup
            test_helper_scripts
            cleanup_test_environment
            show_test_summary
            ;;
        --deploy)
            log_info "執行部署測試..."
            test_container_startup
            test_deployment_simulation
            test_application_functionality
            cleanup_test_environment
            show_test_summary
            ;;
        --help|-h)
            show_help
            exit 0
            ;;
        "")
            log_info "執行完整測試套件..."
            test_docker_environment
            test_file_structure
            test_file_permissions
            test_container_build
            test_container_startup
            test_deployment_simulation
            test_application_functionality
            test_helper_scripts
            cleanup_test_environment
            show_test_summary
            ;;
        *)
            echo "未知選項: $1"
            show_help
            exit 1
            ;;
    esac
}

main "$@"