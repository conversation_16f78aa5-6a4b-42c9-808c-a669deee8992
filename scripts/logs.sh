#!/bin/bash

# 日誌查看腳本

set -e

# 顏色定義
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 檢查容器是否運行
check_container() {
    if ! docker-compose ps | grep -q "Up"; then
        log_error "容器未運行，請先執行 ./scripts/setup.sh"
        exit 1
    fi
}

# 顯示部署日誌
show_deployment_logs() {
    log_info "查看部署日誌..."
    
    # 查找最新的部署日誌
    local log_file=$(docker-compose exec codedeploy-sim bash -c "find /opt/codedeploy-agent/deployment-root -name 'scripts.log' -type f -printf '%T@ %p\n' 2>/dev/null | sort -n | tail -1 | cut -d' ' -f2-" 2>/dev/null)
    
    if [ -n "$log_file" ]; then
        log_success "找到部署日誌: $log_file"
        echo ""
        echo "=== 部署日誌內容 ==="
        docker-compose exec codedeploy-sim cat "$log_file"
        echo "=== 日誌結束 ==="
    else
        log_error "未找到部署日誌，可能尚未執行部署"
        echo ""
        echo "請先執行部署："
        echo "  ./scripts/deploy.sh"
    fi
}

# 顯示容器日誌
show_container_logs() {
    log_info "查看容器日誌..."
    echo ""
    docker-compose logs codedeploy-sim
}

# 即時跟蹤日誌
follow_logs() {
    log_info "即時跟蹤容器日誌（按 Ctrl+C 退出）..."
    echo ""
    docker-compose logs -f codedeploy-sim
}

# 顯示所有部署歷史
show_deployment_history() {
    log_info "查看部署歷史..."
    echo ""
    
    docker-compose exec codedeploy-sim bash -c '
        if [ -d "/opt/codedeploy-agent/deployment-root" ]; then
            echo "部署歷史："
            find /opt/codedeploy-agent/deployment-root -type d -name "d-*-local" | sort | while read dir; do
                timestamp=$(basename "$dir" | sed "s/d-//g" | sed "s/-local//g")
                if [ -f "$dir/logs/scripts.log" ]; then
                    status="成功"
                    if grep -q "失敗\|ERROR\|error" "$dir/logs/scripts.log" 2>/dev/null; then
                        status="失敗"
                    fi
                else
                    status="未知"
                fi
                echo "  $timestamp - $status"
            done
        else
            echo "尚無部署歷史"
        fi
    '
}

# 顯示當前部署狀態
show_deployment_status() {
    log_info "查看當前部署狀態..."
    echo ""
    
    docker-compose exec codedeploy-sim bash -c '
        echo "符號連結狀態："
        if [ -L "/kkcorp/kktv-drm" ]; then
            echo "  ✓ /kkcorp/kktv-drm -> $(readlink /kkcorp/kktv-drm)"
        else
            echo "  ✗ /kkcorp/kktv-drm 不存在"
        fi
        
        echo ""
        echo "應用文件狀態："
        if [ -f "/kkcorp/kktv-drm/server.js" ]; then
            echo "  ✓ server.js 存在"
        else
            echo "  ✗ server.js 不存在"
        fi
        
        if [ -f "/kkcorp/kktv-drm/package.json" ]; then
            echo "  ✓ package.json 存在"
        else
            echo "  ✗ package.json 不存在"
        fi
        
        echo ""
        echo "最新部署目錄："
        latest_dir=$(find /opt/codedeploy-agent/deployment-root -type d -name "d-*-local" | sort | tail -1)
        if [ -n "$latest_dir" ]; then
            echo "  $latest_dir"
            echo "  目錄大小: $(du -sh "$latest_dir" | cut -f1)"
        else
            echo "  無部署目錄"
        fi
    '
}

# 顯示使用說明
show_help() {
    echo "使用方法："
    echo "  $0                # 顯示最新部署日誌"
    echo "  $0 --container    # 顯示容器日誌"
    echo "  $0 --follow       # 即時跟蹤容器日誌"
    echo "  $0 --history      # 顯示部署歷史"
    echo "  $0 --status       # 顯示當前部署狀態"
    echo "  $0 --help         # 顯示此說明"
}

# 主函數
main() {
    case "$1" in
        --container)
            check_container
            show_container_logs
            ;;
        --follow|-f)
            check_container
            follow_logs
            ;;
        --history)
            check_container
            show_deployment_history
            ;;
        --status)
            check_container
            show_deployment_status
            ;;
        --help|-h)
            show_help
            exit 0
            ;;
        "")
            check_container
            show_deployment_logs
            ;;
        *)
            echo "未知選項: $1"
            show_help
            exit 1
            ;;
    esac
}

main "$@"