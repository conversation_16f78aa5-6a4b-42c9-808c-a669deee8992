#!/bin/bash

# 環境清理腳本

set -e

# 顏色定義
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 停止容器
stop_containers() {
    log_info "停止 Docker 容器..."
    
    if docker-compose ps | grep -q "Up"; then
        docker-compose down
        log_success "容器已停止"
    else
        log_info "容器已經停止"
    fi
}

# 清理容器和映像
cleanup_docker() {
    log_info "清理 Docker 資源..."
    
    # 移除容器
    docker-compose down --remove-orphans
    
    # 移除映像（可選）
    if [ "$1" = "--images" ]; then
        log_warning "移除 Docker 映像..."
        docker-compose down --rmi all --remove-orphans
        log_success "Docker 映像已移除"
    fi
    
    # 清理 volumes
    if [ "$1" = "--volumes" ] || [ "$2" = "--volumes" ]; then
        log_warning "移除 Docker volumes..."
        docker-compose down -v --remove-orphans
        log_success "Docker volumes 已移除"
    fi
}

# 清理部署目錄（在容器內）
cleanup_deployments() {
    if docker-compose ps | grep -q "Up"; then
        log_info "清理容器內的部署目錄..."
        docker-compose exec codedeploy-sim bash -c "rm -rf /opt/codedeploy-agent/deployment-root/*" 2>/dev/null || true
        docker-compose exec codedeploy-sim bash -c "rm -f /kkcorp/kktv-drm" 2>/dev/null || true
        log_success "部署目錄已清理"
    else
        log_info "容器未運行，跳過部署目錄清理"
    fi
}

# 顯示清理選項
show_cleanup_options() {
    echo "可用的清理選項："
    echo ""
    echo "1. 僅停止容器:"
    echo "   $0 --stop"
    echo ""
    echo "2. 清理部署目錄（保留容器）:"
    echo "   $0 --deployments"
    echo ""
    echo "3. 完全清理（停止容器 + 移除 volumes）:"
    echo "   $0 --full"
    echo ""
    echo "4. 深度清理（包含 Docker 映像）:"
    echo "   $0 --deep"
    echo ""
    echo "5. 自定義清理:"
    echo "   $0 --images          # 移除映像"
    echo "   $0 --volumes         # 移除 volumes"
    echo ""
}

# 確認操作
confirm_action() {
    local action="$1"
    
    echo -e "${YELLOW}警告:${NC} 您即將執行: $action"
    echo "這個操作不可逆，確定要繼續嗎？ (y/N)"
    
    read -r response
    case "$response" in
        [yY][eE][sS]|[yY]) 
            return 0
            ;;
        *)
            log_info "操作已取消"
            exit 0
            ;;
    esac
}

# 顯示使用說明
show_help() {
    echo "環境清理腳本"
    echo ""
    echo "使用方法："
    echo "  $0 --stop        # 僅停止容器"
    echo "  $0 --deployments # 清理部署目錄"
    echo "  $0 --full        # 完全清理"
    echo "  $0 --deep        # 深度清理（包含映像）"
    echo "  $0 --images      # 移除 Docker 映像"
    echo "  $0 --volumes     # 移除 Docker volumes"
    echo "  $0 --help        # 顯示此說明"
    echo ""
    show_cleanup_options
}

# 主函數
main() {
    case "$1" in
        --stop)
            stop_containers
            ;;
        --deployments)
            cleanup_deployments
            ;;
        --full)
            confirm_action "完全清理（停止容器 + 移除 volumes）"
            stop_containers
            cleanup_docker --volumes
            log_success "完全清理完成"
            ;;
        --deep)
            confirm_action "深度清理（停止容器 + 移除映像和 volumes）"
            stop_containers
            cleanup_docker --images --volumes
            log_success "深度清理完成"
            ;;
        --images)
            confirm_action "移除 Docker 映像"
            cleanup_docker --images
            ;;
        --volumes)
            confirm_action "移除 Docker volumes"
            cleanup_docker --volumes
            ;;
        --help|-h)
            show_help
            exit 0
            ;;
        "")
            show_cleanup_options
            ;;
        *)
            echo "未知選項: $1"
            show_help
            exit 1
            ;;
    esac
}

main "$@"