#!/bin/bash

# 快速部署腳本

set -e

# 顏色定義
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 檢查容器是否運行
check_container() {
    if ! docker-compose ps | grep -q "Up"; then
        log_error "容器未運行，請先執行 ./scripts/setup.sh"
        exit 1
    fi
}

# 執行部署
run_deployment() {
    local clean_flag=""
    
    if [ "$1" = "--clean" ]; then
        clean_flag="--clean"
        log_info "將清理之前的部署..."
    fi
    
    log_info "執行 CodeDeploy 部署模擬..."
    
    if docker-compose exec codedeploy-sim ./simulate-codedeploy.sh $clean_flag; then
        log_success "部署完成！"
        echo ""
        echo "檢查部署結果："
        echo "  docker-compose exec codedeploy-sim ls -la /kkcorp/"
        echo ""
        echo "查看應用日誌："
        echo "  ./scripts/logs.sh"
        echo ""
        echo "測試應用："
        echo "  docker-compose exec codedeploy-sim bash -c 'cd /kkcorp/kktv-drm && node server.js'"
    else
        log_error "部署失敗！"
        echo ""
        echo "查看錯誤日誌："
        echo "  ./scripts/logs.sh"
        echo ""
        echo "進入容器調試："
        echo "  docker-compose exec codedeploy-sim bash"
        exit 1
    fi
}

# 顯示使用說明
show_help() {
    echo "使用方法："
    echo "  $0            # 執行部署"
    echo "  $0 --clean    # 清理後重新部署"
    echo "  $0 --help     # 顯示此說明"
}

# 主函數
main() {
    case "$1" in
        --help|-h)
            show_help
            exit 0
            ;;
        --clean)
            check_container
            run_deployment --clean
            ;;
        "")
            check_container
            run_deployment
            ;;
        *)
            echo "未知選項: $1"
            show_help
            exit 1
            ;;
    esac
}

main "$@"