#!/bin/bash

# 診斷腳本 - 檢查部署問題
set -e

echo "=== 診斷 kktv-drm 部署問題 ==="
echo ""

echo "1. 檢查 npm install 是否成功..."
if [ -d "/kkcorp/kktv-drm/node_modules" ]; then
    echo "✓ node_modules 目錄存在"
    echo "已安裝的關鍵模組："
    ls -la /kkcorp/kktv-drm/node_modules/ | grep drm-plugin || echo "❌ 找不到 drm-plugin 模組"
else
    echo "❌ node_modules 目錄不存在"
fi
echo ""

echo "2. 檢查 C++ 編譯工具..."
node-gyp --version 2>/dev/null && echo "✓ node-gyp 可用" || echo "❌ node-gyp 不可用"
g++ --version 2>/dev/null && echo "✓ g++ 可用" || echo "❌ g++ 不可用"
echo ""

echo "3. 檢查應用檔案..."
if [ -f "/kkcorp/kktv-drm/drm_server.js" ]; then
    echo "✓ drm_server.js 存在"
else
    echo "❌ drm_server.js 不存在"
fi
echo ""

echo "4. 檢查 upstart 服務狀態..."
if [ -f "/etc/init/kktv-drm.conf" ]; then
    echo "✓ upstart 配置檔存在"
    echo "服務狀態："
    status kktv-drm 2>/dev/null || echo "❌ upstart 服務無法查詢狀態"
else
    echo "❌ upstart 配置檔不存在"
fi
echo ""

echo "5. 檢查運行中的進程..."
echo "Node.js 進程："
ps aux | grep node | grep -v grep || echo "❌ 沒有找到 Node.js 進程"
echo ""

echo "6. 檢查埠口監聽..."
echo "監聽中的埠口："
netstat -tlnp 2>/dev/null | grep :8000 || echo "❌ 埠口 8000 沒有被監聽"
echo ""

echo "7. 嘗試手動啟動服務..."
if [ -d "/kkcorp/kktv-drm" ]; then
    cd /kkcorp/kktv-drm
    echo "嘗試手動執行 node drm_server.js (5秒後終止)..."
    timeout 5 node drm_server.js 2>&1 || echo "手動啟動失敗或有錯誤"
else
    echo "❌ 無法進入 /kkcorp/kktv-drm 目錄"
fi
echo ""

echo "=== 診斷完成 ==="