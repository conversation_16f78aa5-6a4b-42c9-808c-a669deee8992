[general]

[/var/log/upstart/kktv-drm.log]
datetime_format = %Y-%m-%d %H:%M:%S
file = /var/log/upstart/kktv-drm.log
buffer_duration = 5000
log_stream_name = {hostname}
initial_position = end_of_file
log_group_name = /aws/ec2/kktv-drm

[/var/log/nginx/access.log]
datetime_format = %Y-%m-%d %H:%M:%S
file = /var/log/nginx/access.log
buffer_duration = 5000
log_stream_name = {hostname}
initial_position = end_of_file
log_group_name = /aws/ec2/kktv-drm.access

[/var/log/nginx/error.log]
datetime_format = %Y-%m-%d %H:%M:%S
file = /var/log/nginx/error.log
buffer_duration = 5000
log_stream_name = {hostname}
initial_position = end_of_file
log_group_name = /aws/ec2/kktv-drm.error
