- hosts: localhost
  connection: local
  vars:
    env: "{{ ansible_hostname.split('-')[1] }}"
  remote_user: root
  tasks:
  # create /kkcorp
  - file: path=/kkcorp state=directory owner=root group=root
  - lineinfile: dest=/etc/environment line="INSTANCE_ENV={{ env | default('test') }}"

  # setup nodejs
  - command: bash -c 'curl -sL https://deb.nodesource.com/setup_4.x | sudo -E bash -'
  - command: apt-get update
  - command: apt-get --allow-unauthenticated install -y nodejs
  - apt: name=build-essential state=present
  - apt: name=g++ state=present
  - command: npm install -g node-gyp@4.0.0

  # setup awslogs
  - file: path=/etc/awslogs state=directory owner=root group=root
  - copy: src=awslogs/awslogs.conf dest=/etc/awslogs/awslogs.conf
  - command: curl https://s3.amazonaws.com/aws-cloudwatch/downloads/latest/awslogs-agent-setup.py -O
  - command: python ./awslogs-agent-setup.py -n -r ap-northeast-1 -c /etc/awslogs/awslogs.conf
  - service: name=awslogs state=started enabled=yes

  # setup drm service
  - copy: src=upstart/kktv-drm.conf dest=/etc/init/kktv-drm.conf
  - service: name=kktv-drm state=started enabled=yes

  # setup nginx
  - apt: name=nginx state=present
  - copy: src=nginx/nginx.conf dest=/etc/nginx/sites-enabled/default
  - service: name=nginx state=started enabled=yes

  # setup monit
  - apt: name=monit state=present
  - copy: src=monit/awslogs dest=/etc/monit/conf.d/awslogs
  - copy: src=monit/kktv-drm dest=/etc/monit/conf.d/kktv-drm
  - copy: src=monit/nginx dest=/etc/monit/conf.d/nginx
  - command: /usr/bin/monit reload
