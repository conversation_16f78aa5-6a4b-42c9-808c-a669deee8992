const querystring = require('querystring');
const http = require('https');
const crypto = require('crypto');
const url = require('url');
const util = require('util');

var pluginConfig = null;
var globalConfig = null;

var nexus_player_special_request = new Buffer([ 8, 4 ]);

var widevine = module.exports = function(general_config, plugin_config) {
    globalConfig = general_config;
    pluginConfig = plugin_config;

    if(!general_config || !plugin_config) throw new Error('No Configuration');
    if(!plugin_config.provider) throw new Error('No Provider');
    if(!plugin_config.license_server) throw new Error('No License Server');
};

/**requestMatch(request, postBody, callback):
 *
 * test the request match the DRM provided by the plugin or not
 *
 * error:
 *      PLUGIN_NOT_COMPATIBLE
 *      PLUGIN_NOT_PROCESSABLE
 *
 * return value:
 *     license object:  {
 *       licenseRequest request to license server
 *       licenseObject  internal license object to process (option)
 *       keyId          processed key ID, format: 16bytes in hex format
 *       needIV         should set IV to this object
 *       IvId           IV ID: 16bytes in hex format
 *
 *       key            real Key to this request
 *       IV             real IV to this request
 *       expireTime     expire time on unix time
 *     }
 */
widevine.requestMatch = function(req, body, callback) {
    process.nextTick(_ => {
        // NO PROCESS THE REQUEST, GET IT OUT
        if(body.equals(nexus_player_special_request)) {
            return callback(null, {
                licenseRequest: body,
                specialLicense: true,
            });
            globalConfig.logger(globalConfig.LOG.INFO,{event:"test_drm",failed:"not support nexus player special request"});
            return callback(globalConfig.ERROR.PLUGIN_NOT_PROCESSABLE, null);
        }

        if(body[0] == 8 && body[1] == 1) {
            var license = decode_protobuf(body);
            var custom_data = {};
            try {
                globalConfig.logger(globalConfig.LOG.INFO,{event:"test_drm",pass:"detect widevine",key_id:license[2][2][1][1][2]._orig.toString('hex')});
                try {
                    if(Array.isArray(license[2][1][3])) {
                        license[2][1][3].forEach(function(v) {
                            custom_data[v[1]._orig.toString()] = v[2]._orig.toString();
                        });
                    }
                } catch(e) {}

                return callback(null, {
                    licenseRequest: body,
                    keyId: license[2][2][1][1][2]._orig.toString('hex'),
                    customdata: querystring.stringify(custom_data)
                });
            } catch(e) {
            }
        }

        return callback(globalConfig.ERROR.PLUGIN_NOT_COMPATIBLE, null);
    });
}

/*
 * return license
 */
widevine.onLicenseAcquire = function(clientPlatform, licenseObject, callback) {
    process.nextTick(_ => {
        var message = {
            payload: licenseObject.licenseRequest.toString('base64'),
            provider: pluginConfig.provider.name,
            content_id: 'kkbox',
            allowed_track_types: 'SD_HD',
            use_policy_overrides_exclusively: true,
        };

        if(licenseObject.specialLicense !== true) {
            if(!licenseObject.key || licenseObject.key.length != 32) {
                globalConfig.logger(globalConfig.LOG.INFO,{event:"license_acquire",failed:"key format error"});
                return callback(globalConfig.ERROR.NO_SUPPLY_KEY, null);
            }

            if(licenseObject.expireTime === undefined) {
                globalConfig.logger(globalConfig.LOG.INFO,{event:"license_acquire",failed:"no expire time"});
                return callback(globalConfig.ERROR.NO_SUPPLY_EXPIRETIME, null);
            }

            message.content_key_specs = [{
                "key_id": (new Buffer(licenseObject.keyId, 'hex')).toString('base64'),
                "track_type":"SD"
            }];

            message.policy_overrides = {
                license_duration_seconds: Math.floor(licenseObject.expireTime - ((new Date).getTime() / 1000)), // + 4 * 3600,
                can_play: true
            };
        };

        // customdata: architecture_name=x86-32&company_name=Google&model_name=ChromeCDM&platform_name=Windows
        globalConfig.logger(globalConfig.LOG.INFO,{event:"license_acquire",subevent:"build message request",message:message});
        // assign key after logger, we don't want key appeared in the log
        if(licenseObject.specialLicense !== true) {
            message.content_key_specs[0].key = (new Buffer(licenseObject.key, 'hex')).toString('base64');

            if(pluginConfig.hdcp)
                message.content_key_specs[0].hdcp = pluginConfig.hdcp;
            if(pluginConfig.cgms)
                message.content_key_specs[0].cgms_flags = pluginConfig.cgms_flags;
        }

        message = JSON.stringify(message);
        var sha = crypto.createHash('sha1');
        sha.update(message);

        message = (new Buffer(message)).toString('base64');
        var aes = crypto.createCipheriv('aes-256-cbc', new Buffer(pluginConfig.provider.key, 'hex'), new Buffer(pluginConfig.provider.iv, 'hex'));
        aes.setAutoPadding(true);
        var signature = aes.update(sha.digest('binary'));
        signature = Buffer.concat( [ signature, aes.final() ] );

        var lic_request = JSON.stringify({
          request: message,
          signature: signature.toString('base64'),
          signer: pluginConfig.provider.name
        });

        var license_server = url.parse(pluginConfig.license_server + '/' + pluginConfig.provider.name);
        license_server.method = 'POST';
        license_server.headers = { 'Content-Type': 'application/json' };

        http.request(license_server, function(response) {
            var body = [];
            response.on('data', function(chunk) {
                body.push(chunk);
            }).on('end', function() {
                var rep = Buffer.concat(body).toString('utf-8');
                var decoded = JSON.parse(rep);
                if(decoded !== null) {
                    globalConfig.logger(globalConfig.LOG.INFO,{event:"license_acquire",subevent:"response",decode_response:decoded,client_platform:clientPlatform});
                } else {
                    globalConfig.logger(globalConfig.LOG.INFO,{event:"license_acquire",subevent:"response",response:rep,client_platform:clientPlatform});
                }

                if(response.statusCode == 200 && decoded['status'] === 'OK') {
                    return callback(null, [ new Buffer(decoded["license"], 'base64'), { 'Content-Type': 'text/plain' }] );
                } else {
                    return callback(null, rep);
                }
            });
        }).end(lic_request);
    });
};

widevine.releaseLicenseObject = function(licenseObject) {
};


function decode_protobuf(data, pos, len)
{
    var ret = {}, id, shift, c, type, value, decode;
    if(pos === undefined) pos = 0;
    if(len === undefined) len = data.length;

    while(pos < len) {
        id = 0;
        shift = 1;

        do {
            c = data[pos++];
            id += ( c & 0x7f ) * shift;
            shift = shift * 128;
        } while( ( c  & 0x80 ) == 0x80 );

        type = id & 7;
        id = id >> 3;

        switch(type) {
            case 0:
                value = 0; shift = 1;
                do {
                    c = data[pos++];
                    value += ( c & 0x7f ) * shift;
                    shift *= 128;

                } while( ( c & 0x80 ) == 0x80 );
                ret[id] = value;
                break;

            case 1:
                if ( pos + 8 > len  ) {
                    return null;

                }
                ret[id] = data.readDoubleLE(pos, true);
                pos += 8;
                break;

            case 2:
                value = 0; shift = 1;
                do {
                    c = data[pos++];
                    value += ( c & 0x7f ) * shift;
                    shift *= 128;
                } while( ( c & 0x80  ) == 0x80  );

                if(pos + value > len) {
                    return null;
                }

                if(ret[id] && !Array.isArray(ret[id])) {
                    ret[id] = [ ret[id] ];
                }

                decode = decode_protobuf (data, pos, pos + value);
                if(decode === null) {
                    decode = data.slice(pos, pos + value);
                    decode._orig = decode;
                } else {
                    decode._orig = data.slice(pos, pos + value);
                }

                if(Array.isArray(ret[id])) {
                    ret[id].push(decode);
                } else {
                    ret[id] = decode;
                }

                pos += value;
                break;

            case 5:
                if(pos + 4 > len)
                    return null;
                ret[id] = data.readFloatLE(pos, true);
                pos += 4;
                break;

            default:
                return null;
        }
    }
    return ret;
}

