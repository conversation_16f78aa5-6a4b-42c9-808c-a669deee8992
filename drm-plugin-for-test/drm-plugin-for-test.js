const querystring = require('querystring');
var pluginConfig = null;
var globalConfig = null;

var drm_test = module.exports = function(general_config, plugin_config) {
    globalConfig = general_config;
    pluginConfig = plugin_config;

    if(general_config.I_AM_SURELY_THE_MODULE_IS_FOR_UNIT_TEST_ONLY !== "AND NEVER USE THE MODULE ON PRODUCTION ENVIRONMENT")
        throw new Error("Should enable unit test feature");

    if(globalConfig.TEST_CERT) {
        drm_test.getServerCertificate = function() {
            return pluginConfig.TEST_CERT;
        }
    }
};

/**requestMatch(request, postBody, callback):
 *
 * test the request match the DRM provided by the plugin or not
 *
 * error:
 *      PLUGIN_NOT_COMPATIBLE
 *      PLUGIN_NOT_PROCESSABLE
 *
 * return value:
 *     license object:  {
 *       licenseRequest request to license server
 *       licenseObject  internal license object to process (option)
 *       keyId          processed key ID, format: 16bytes in hex format
 *       needIV         should set IV to this object
 *       IvId           IV ID: 16bytes in hex format
 *
 *       key            real Key to this request
 *       IV             real IV to this request
 *       expireTime     expire time on unix time
 *     }
 */
drm_test.requestMatch = function(req, body, callback) {
    process.nextTick(_ => {
        // TEST_KEY=
        if(body[0] == 0x54 && body[1] == 0x45 && body[2] == 0x53 && body[3] == 0x54 && body[4] == 0x5f && body[5] == 0x4b && body[6] == 0x45 && body[7] == 0x59 && body[8] == 0x3d) {
            var qs = querystring.parse(body.toString());
            var result = {
                keyId: qs.TEST_KEY
            };

            if(globalConfig.TEST_IV) {
                result.IvId = qs.TEST_IV;
                result.needIV = true;
            }
            return callback(null, result);
        }

        callback(globalConfig.ERROR.PLUGIN_NOT_COMPATIBLE, null);
        return;
    });
}

/*
 * return license
 */
drm_test.onLicenseAcquire = function(licenseObject, callback) {
    process.nextTick(_ => {
        if(!licenseObject.key || licenseObject.key.length != 32) return callback(globalConfig.ERROR.NO_SUPPLY_KEY, null);
        if(globalConfig.TEST_IV && (!licenseObject.IV || licenseObject.IV.length != 32)) return callback(globalConfig.ERROR.NO_SUPPLY_IV, null);
        if(!licenseObject.expireTime) return callback(globalConfig.ERROR.NO_SUPPLY_EXPIRETIME, null);

        callback(null, JSON.stringify({
            key: licenseObject.key,
            IV: licenseObject.IV,
            expire: licenseObject.expireTime
        }));
    });
};

drm_test.releaseLicenseObject = function(licenseObject) {
};
