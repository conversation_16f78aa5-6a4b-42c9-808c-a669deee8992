# kktv-drm
DRM License Server

version: 0.2.0

## Setup with ansible
sudo ansible-playbook ansible/main.yml --connection=local -i 'localhost,'

## Installation
# Requirement
* Node.js 4.2.0+
* node-gyp
* g++

# fairplay
* private_key: openssl pkcs12 -in FairPlay/FairPlay-KKVideo.p12 -passin pass: -out ooxx.pem -nodes
* server_certificate: base64 -i kktv_secret_files/FairPlay/pem/fairplay.cer
* ask: cat kktv_secret_files/FairPlay/ASk | xxd -r -p - | base64 -
