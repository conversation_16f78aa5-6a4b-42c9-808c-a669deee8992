0 info it worked if it ends with ok
1 verbose cli [ '/Users/<USER>/.nvm/versions/node/v4.9.1/bin/node',
1 verbose cli   '/Users/<USER>/.nvm/versions/node/v4.9.1/bin/npm',
1 verbose cli   'run',
1 verbose cli   'build' ]
2 info using npm@2.15.11
3 info using node@v4.9.1
4 verbose stack Error: missing script: build
4 verbose stack     at run (/Users/<USER>/.nvm/versions/node/v4.9.1/lib/node_modules/npm/lib/run-script.js:142:19)
4 verbose stack     at /Users/<USER>/.nvm/versions/node/v4.9.1/lib/node_modules/npm/lib/run-script.js:58:5
4 verbose stack     at /Users/<USER>/.nvm/versions/node/v4.9.1/lib/node_modules/npm/node_modules/read-package-json/read-json.js:356:5
4 verbose stack     at checkBinReferences_ (/Users/<USER>/.nvm/versions/node/v4.9.1/lib/node_modules/npm/node_modules/read-package-json/read-json.js:320:45)
4 verbose stack     at final (/Users/<USER>/.nvm/versions/node/v4.9.1/lib/node_modules/npm/node_modules/read-package-json/read-json.js:354:3)
4 verbose stack     at then (/Users/<USER>/.nvm/versions/node/v4.9.1/lib/node_modules/npm/node_modules/read-package-json/read-json.js:124:5)
4 verbose stack     at ReadFileContext.<anonymous> (/Users/<USER>/.nvm/versions/node/v4.9.1/lib/node_modules/npm/node_modules/read-package-json/read-json.js:295:20)
4 verbose stack     at ReadFileContext.callback (/Users/<USER>/.nvm/versions/node/v4.9.1/lib/node_modules/npm/node_modules/graceful-fs/graceful-fs.js:78:16)
4 verbose stack     at FSReqWrap.readFileAfterOpen [as oncomplete] (fs.js:303:13)
5 verbose cwd /Users/<USER>/kkws/kktv/kktv-drm/drm-plugin-fairplay
6 error Darwin 23.3.0
7 error argv "/Users/<USER>/.nvm/versions/node/v4.9.1/bin/node" "/Users/<USER>/.nvm/versions/node/v4.9.1/bin/npm" "run" "build"
8 error node v4.9.1
9 error npm  v2.15.11
10 error missing script: build
11 error If you need help, you may report this error at:
11 error     <https://github.com/npm/npm/issues>
12 verbose exit [ 1, true ]
