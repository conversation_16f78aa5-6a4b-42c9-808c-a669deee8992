version: '3.8'
services:
  codedeploy-sim:
    build: .
    container_name: kktv-drm-codedeploy-sim
    hostname: kktv-test-drm
    volumes:
      - ./:/app
      - deployment-root:/opt/codedeploy-agent/deployment-root
    ports:
      - "8000:8000"  # 應用端口
      - "80:80"      # nginx 端口（如果需要）
    environment:
      - DEPLOYMENT_GROUP_ID=local-deployment-group
      - APPLICATION_NAME=kktv-drm
      - INSTANCE_ENV=test
    tty: true
    stdin_open: true
    privileged: true  # 允許服務管理
    working_dir: /app

volumes:
  deployment-root:
    driver: local