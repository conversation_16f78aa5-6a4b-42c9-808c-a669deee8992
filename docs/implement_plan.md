# KKTV DRM 服務器離線 HLS FairPlay 實施計劃

## 1. 概述

本文檔提供了擴展 KKTV DRM 服務器以支援 Offline HLS FairPlay 授權請求的詳細實施計劃。離線 FairPlay 支援允許用戶下載加密內容並在沒有網絡連接的情況下播放，同時仍然遵守授權條款和 DRM 保護。

## 2. 系統需求

- 擴展現有的線上 FairPlay 授權系統以支援離線請求
- 支援設備：iOS/tvOS/iPadOS
- 授權策略：
  - 租賃期（Storage Duration）: 下載後可存放 30 天
  - 播放期（Playback Duration）: 初次播放後 7 天內有效
- 追蹤裝置下載記錄

## 3. 系統設計

### 3.1. 架構概述

在現有的 DRM 服務器上擴展以下功能：

```mermaid
flowchart TD
    subgraph Client
        App[iOS/tvOS App]
        AVContentKeySession[AVContentKeySession]
    end
    
    subgraph KKTV_DRM_Server
        Server[HTTP Server]
        FairPlayPlugin[FairPlay Plugin]
        LicenseDB[License Database]
        DeviceTracker[Device Tracking]
    end
    
    App -->|1. 下載請求| AVContentKeySession
    AVContentKeySession -->|2. SPC 請求| Server
    Server -->|3. 轉發請求| FairPlayPlugin
    FairPlayPlugin -->|4. 檢查授權策略| LicenseDB
    FairPlayPlugin -->|5. 記錄裝置下載| DeviceTracker
    FairPlayPlugin -->|6. 生成持久授權| Server
    Server -->|7. CKC 響應| AVContentKeySession
    AVContentKeySession -->|8. 存儲持久授權| App
```

### 3.2. 主要變更點

1. **FairPlay Plugin 修改**: 擴展以支援持久授權（Persistent License）
2. **授權策略管理**: 在授權響應中添加租賃期和播放期限制
3. **裝置追蹤模組**: 開發新模組以跟踪裝置下載記錄
4. **配置更新**: 添加離線授權配置

## 4. 需要修改的組件和文件

### 4.1. FairPlay Plugin (`drm-plugin-fairplay.js`)

需要增加以下功能：

1. **識別離線請求**:
   - 從 SPC 中檢測請求類型（在線/離線）
   - 解析並處理離線特定參數

2. **生成持久授權**:
   - 修改 `onLicenseAcquire` 函數以支援持久授權
   - 增加租賃期和播放期的處理邏輯

```javascript
// 需要添加的代碼示例
fairplay.onLicenseAcquire = function(clientPlatform, licenseObject, callback) {
    process.nextTick(_ => {
        var fps_data = licenseObject.licenseObject;
        
        // ... 現有代碼 ...
        
        // 檢查是否為離線請求
        if (licenseObject.isOfflineRequest) {
            // 設置持久授權參數
            fps_data.persistent = true;
            fps_data.rentalDuration = CONFIG.OFFLINE_RENTAL_DURATION; // 30天
            fps_data.playbackDuration = CONFIG.OFFLINE_PLAYBACK_DURATION; // 7天
            
            // 記錄裝置下載
            trackDeviceDownload(licenseObject.deviceId, licenseObject.keyId);
        }
        
        // ... 生成授權 ...
    });
};
```

### 4.2. 原生模組 (`fpssdk.cpp`)

修改原生 FairPlay 模組以支援持久授權類型。主要修改點包括：

1. **增加持久授權參數**:
   - 在 `FPSGenerateLicense` 函數中添加持久授權支援
   - 設置正確的 keyType 值以指示持久授權

```cpp
// 需要修改的代碼（在 FPSGenerateLicense 函數中）
static void FPSGenerateLicense(ARGS)
{
    // ... 現有代碼 ...
    
    // 檢查是否為持久授權
    bool isPersistent = false;
    FOREACH_OBJ(self, key, val, {
        // ... 現有代碼 ...
        if (key == "persistent") {
            isPersistent = val->BooleanValue();
        } else if (key == "playbackDuration") {
            session->serverCtx.ckcContainer.ckcData.keyDuration.playbackDuration = val->Uint32Value();
        }
    });
    
    // 租賃期和播放期設置
    if (isPersistent) {
        // 設置為持久授權類型
        if (session->serverCtx.ckcContainer.ckcData.keyDuration.rentalDuration != 0 &&
            session->serverCtx.ckcContainer.ckcData.keyDuration.playbackDuration != 0) {
            // 設置为具有租賃期和播放期的持久授權
            session->serverCtx.ckcContainer.ckcData.keyDuration.keyType = 0x27b59bde;
        } else if (session->serverCtx.ckcContainer.ckcData.keyDuration.playbackDuration != 0) {
            // 僅有播放期的持久授權
            session->serverCtx.ckcContainer.ckcData.keyDuration.keyType = 0x1a4bde7e;
        }
    }
    
    // ... 其餘代碼 ...
}
```

### 4.3. HTTP 伺服器 (`index.js`)

修改主 HTTP 伺服器以處理離線請求特定參數，主要對裝置識別和請求類型進行處理：

```javascript
// 示例修改
// 在 request 處理中添加設備識別符檢測
if (request.method == 'POST') {
    // ... 現有代碼 ...
    
    // 提取裝置 ID
    var deviceId = request.headers['x-device-id'] || qs.device_id;
    
    // 檢查是否是離線請求
    var isOfflineRequest = (qs.request_type === 'offline' || 
                            licenseObject.customdata?.indexOf('offline=true') !== -1);
    
    // 附加到 licenseObject
    if (isOfflineRequest) {
        licenseObject.isOfflineRequest = true;
        licenseObject.deviceId = deviceId;
    }
    
    // ... 其餘代碼 ...
}
```

### 4.4. 配置文件

在配置中添加離線授權的參數：

**`config/drm-plugin-fairplay.prod.json`** 和 **`config/drm-plugin-fairplay.test.json`**:

```json
{
  "private_key": "...",
  "ask": "...",
  "server_certificate": "...",
  "offline_support": true,
  "offline_rental_duration": 2592000,  // 30天(秒)
  "offline_playback_duration": 604800,  // 7天(秒)
  "max_offline_devices_per_user": 5
}
```

### 4.5. 裝置追蹤模組 (新模組)

創建新的裝置追蹤模組來記錄離線下載：

**`device-tracking/index.js`**:

```javascript
const db = require('../db/connection');  // 假設使用某種資料庫連接

exports.trackDeviceDownload = async function(deviceId, userId, contentId, keyId) {
    try {
        // 記錄下載
        await db.query(
            `INSERT INTO offline_downloads 
            (device_id, user_id, content_id, key_id, downloaded_at, rental_expires_at) 
            VALUES (?, ?, ?, ?, NOW(), DATE_ADD(NOW(), INTERVAL 30 DAY))`,
            [deviceId, userId, contentId, keyId]
        );
        
        // 檢查下載數量限制
        const count = await db.query(
            `SELECT COUNT(*) as count FROM offline_downloads 
             WHERE user_id = ? AND rental_expires_at > NOW()`,
            [userId]
        );
        
        return { 
            success: true, 
            downloadCount: count[0].count 
        };
    } catch (err) {
        console.error('Error tracking device download:', err);
        return { success: false, error: err.message };
    }
};

exports.cleanExpiredDownloads = async function() {
    try {
        await db.query(
            `DELETE FROM offline_downloads WHERE rental_expires_at < NOW()`
        );
        return { success: true };
    } catch (err) {
        console.error('Error cleaning expired downloads:', err);
        return { success: false, error: err.message };
    }
};
```

## 5. 資料庫結構 (新)

建立資料表來追蹤裝置下載：

```sql
CREATE TABLE offline_downloads (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    device_id VARCHAR(255) NOT NULL,
    user_id VARCHAR(255) NOT NULL,
    content_id VARCHAR(255) NOT NULL,
    key_id VARCHAR(255) NOT NULL,
    downloaded_at TIMESTAMP NOT NULL,
    rental_expires_at TIMESTAMP NOT NULL,
    playback_started_at TIMESTAMP NULL,
    playback_expires_at TIMESTAMP NULL,
    INDEX idx_user_device (user_id, device_id),
    INDEX idx_content (content_id),
    INDEX idx_expires (rental_expires_at)
);
```

## 6. 實施步驟

### 階段 1: 離線授權基礎框架 (優先順序: 高)

1. 修改 FairPlay Plugin 以識別離線請求
2. 更新 onLicenseAcquire 函數添加持久授權支援
3. 更新原生模組以支援持久授權參數
4. 修改配置文件結構以包含離線授權參數

### 階段 2: 授權策略管理 (優先順序: 高)

1. 實現租賃期和播放期邏輯
2. 更新 CKC 生成以包含持久授權標記和時間限制
3. 測試授權策略在不同情境下的行為

### 階段 3: 裝置追蹤系統 (優先順序: 中)

1. 設計並實現裝置追蹤資料庫
2. 開發裝置追蹤 API
3. 整合裝置追蹤到授權請求流程中

### 階段 4: 系統整合與優化 (優先順序: 中)

1. 整合所有組件
2. 性能優化和錯誤處理
3. 記錄和監控增強

### 階段 5: 測試與部署 (優先順序: 高)

1. 開發離線播放測試應用
2. 進行系統測試和壓力測試
3. 分階段部署到測試環境和生產環境

## 7. 安全性與兼容性考慮

### 7.1. 安全性考慮

1. **裝置驗證**:
   - 實現強健的裝置識別與驗證機制
   - 考慮添加設備指紋或唯一識別符

2. **重放攻擊防護**:
   - 確保離線請求不能被重放
   - 實現請求唯一性驗證

3. **授權漏洞修復**:
   - 確保離線授權不能被修改或延長
   - 使用適當的加密和簽名機制

### 7.2. 兼容性考慮

1. **舊版客戶端**:
   - 確保修改不會影響現有的線上請求
   - 提供適當的降級機制

2. **iOS/tvOS 版本**:
   - 測試不同 iOS/tvOS 版本上的兼容性
   - 確保在所有目標平台上正常工作

3. **HLS 播放列表兼容性**:
   - 確保生成的 HLS 清單符合 Apple 規範
   - 正確實現 `#EXT-X-SESSION-KEY` 標籤

## 8. 測試策略

### 8.1. 單元測試

1. FairPlay Plugin 離線請求處理
2. 持久授權生成與驗證
3. 裝置追蹤模組

### 8.2. 整合測試

1. 授權請求流程測試
2. 授權策略應用與驗證
3. 裝置追蹤與限制執行

### 8.3. 客戶端測試

1. 建立測試客戶端應用
2. 測試下載和離線播放功能
3. 測試授權限制和過期機制

### 8.4. 性能測試

1. 負載測試授權伺服器
2. 模擬大量並發請求
3. 測量持久授權生成效能

## 9. 監控與維護

1. 添加離線授權請求的監控指標
2. 實現授權使用和過期的報告
3. 設置裝置下載記錄的清理和維護程序

## 10. 結論

通過實施本計劃中的變更，KKTV DRM 服務器將能夠支援 Offline HLS FairPlay 授權請求，允許用戶下載加密內容並在離線狀態下播放，同時維護適當的內容保護和授權策略。這些修改最大程度地保留了現有系統的架構和功能，同時擴展其功能以滿足離線播放的需求。

---

## 附錄 A: API 規範

### A.1. 離線授權請求

**請求:**
```
POST /fairplay_license
Headers:
  - X-Device-ID: [裝置唯一識別符]
  - X-Custom-Data: token_type=playback&token_value=[token]&request_type=offline
Body: [SPC 資料]
```

**響應:**
```
CKC 資料，包含持久授權
```

### A.2. 裝置追蹤 API

**記錄下載:**
```
POST /api/tracking/download
Body:
{
  "device_id": "[裝置ID]",
  "user_id": "[用戶ID]",
  "content_id": "[內容ID]",
  "key_id": "[金鑰ID]"
}
```

**查詢裝置下載:**
```
GET /api/tracking/downloads?user_id=[用戶ID]