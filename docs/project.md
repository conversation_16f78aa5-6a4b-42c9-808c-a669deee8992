# KKTV DRM Server Project Analysis

## 1. HTTP API Endpoints in `index.js`

### Main Endpoints
- `GET /status` — Health check endpoint. Returns 200 OK.
- `OPTIONS *` — CORS preflight handling for all endpoints.
- `POST *` — Main DRM license acquisition endpoint. Handles all POST requests regardless of path (URL is not checked for POST).

## 2. Responsibility for Each API Endpoint

### `/status`
- **Responsibility:** Health check, used for monitoring if the server is alive.

### `OPTIONS` (any path)
- **Responsibility:** Handles CORS preflight requests, sets appropriate headers for cross-origin requests.

### `POST` (any path)
- **Responsibility:**
    - Handles DRM license requests for all supported DRM solutions (FairPlay, Widevine, PlayReady, etc.).
    - Extracts tokens and custom data from headers, query string, or request body.
    - Determines which DRM plugin should handle the request (based on registered plugins and request matching).
    - Validates tokens and permissions.
    - Forwards the request to the appropriate DRM plugin for license generation.
    - Handles key/IV fetching, license expiration, and error handling.

## 3. Implementation Architecture & Module Relationships

### High-Level Structure
- `index.js` — Main server logic, HTTP handling, plugin registration, routing, and orchestration.
- `drm_server.js` / `server.js` — Entry points that configure and start the server, register plugins.
- `drm-plugin-<type>` directories — Each DRM solution (FairPlay, Widevine, PlayReady) is implemented as a plugin in its own directory.
- `config/` — Configuration files for each DRM plugin/environment.

### Module Relationships
- The server loads and registers DRM plugins (e.g., FairPlay) via the `register()` function.
- Each plugin exposes a standard interface: `requestMatch`, `onLicenseAcquire`, etc.
- For a license request:
    1. The server receives a POST request.
    2. It iterates through registered plugins, calling `requestMatch` to see which plugin can handle the request.
    3. The matched plugin returns a `licenseObject` describing the request.
    4. The server fetches keys/IVs as needed, sets expiration, and calls the plugin’s `onLicenseAcquire` to generate the license.
    5. The license is returned to the client.

## 4. FairPlay Implementation Deep Dive

### FairPlay Plugin (`drm-plugin-fairplay/drm-plugin-fairplay.js`)
- **Initialization:**
    - Requires private key, ASK (application secret key), and server certificate from config.
    - Initializes the native FairPlay module (`fairplaystreaming.node`).

- **Key Methods:**
    - `requestMatch(req, body, callback)`
        - Checks if the request is a FairPlay license request (by inspecting the body format).
        - Parses the SPC (Server Playback Context) and asset ID.
        - Returns a `licenseObject` with key/IV requirements and metadata.
    - `onLicenseAcquire(clientPlatform, licenseObject, callback)`
        - Accepts the key, IV, and expiration info.
        - Uses the native module to generate the CKC (Content Key Context, i.e., the license response).
        - Returns the CKC in either binary or XML format, depending on the request.
    - `getServerCertificate()` — Returns the FairPlay server certificate.
    - `releaseLicenseObject()` — Cleans up any resources (no-op in this implementation).

- **Native Module:**
    - The heavy lifting (SPC parsing, CKC generation, crypto) is handled in C++/C via `fairplaystreaming.node` (see `src/`).
    - Node.js plugin acts as a bridge between server and native code.

### FairPlay API Service Flow
1. Client sends a POST request with an SPC and asset ID (or in raw SPC format).
2. Server routes the request to the FairPlay plugin (if `requestMatch` succeeds).
3. Plugin parses the request, extracts asset ID, and indicates the need for key/IV.
4. Server fetches the key/IV (from key server or config).
5. Server calls `onLicenseAcquire` with all required info.
6. Plugin generates the CKC using the native module and returns it to the client (binary or XML-wrapped).

## 4.1 FairPlay Module/Component Relationship

```mermaid
flowchart TD
    subgraph NodeJS_Server
        A[index.js HTTP Server]
        B[drm-plugin-fairplay.js Plugin]
    end
    C[Native Module: fairplaystreaming.node]
    D[C/C++: FPSSession, SKDServer, etc.]
    E[Config Files]

    A -->|registers| B
    B -->|requires| C
    C --> D
    B --> E
```

## 4.2 FairPlay License Request Data Flow

```mermaid
sequenceDiagram
    participant Client
    participant Server as index.js
    participant Plugin as drm-plugin-fairplay.js
    participant Native as fairplaystreaming.node
    participant KeyServer

    Client->>Server: POST / (SPC, asset_id)
    Server->>Plugin: requestMatch(req, body)
    Plugin-->>Server: licenseObject (need key/IV)
    Server->>KeyServer: Fetch key/IV
    KeyServer-->>Server: key, IV
    Server->>Plugin: onLicenseAcquire(..., licenseObject)
    Plugin->>Native: GenerateLicense(key, IV, ...)
    Native-->>Plugin: CKC (license)
    Plugin-->>Server: CKC
    Server-->>Client: CKC (binary or XML)
```

## 4.3 Core Native Class/Struct Relationships

```mermaid
classDiagram
    class FPSSession {
        - SKDServerCtxV1 serverCtx
        - OSStatus lastStatus
        + GenerateLicense()
        + SetASK()
        + SetPrivateKey()
    }
    class SKDServerCtxV1 {
        - SPC Container
        - CKC Container
        - Key/IV
    }
    FPSSession --> SKDServerCtxV1
    SKDServerCtxV1 --> SPC_Container
    SKDServerCtxV1 --> CKC_Container
```

## 5. Summary Table: Main Relationships
| Component                 | Role                                                  |
|---------------------------|-------------------------------------------------------|
| index.js                  | HTTP server, plugin orchestration, routing            |
| drm_server.js/server.js   | Server startup, config, plugin registration           |
| drm-plugin-fairplay.js    | FairPlay plugin logic, native module bridge           |
| fairplaystreaming.node    | Native FairPlay DRM implementation (C/C++)            |
| config/                   | Per-plugin configuration                              |

## 6. References
- [FairPlay Streaming Programming Guide (Apple)](https://developer.apple.com/documentation/fairplaystreaming)
- Project source code: `index.js`, `drm-plugin-fairplay/`, etc.

---

This document provides a technical overview of the KKTV DRM server, its API endpoints, architecture, and in-depth FairPlay handling. For further details, see the referenced source files or request deeper dives into specific modules.