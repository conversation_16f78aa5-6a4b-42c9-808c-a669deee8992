# 系統如何實現 Offline Fairplay HLS

## Encoding (編碼) 階段

### 用於 Online 與 Offline 對於 HLS output 的差異

在 Apple FairPlay DRM 保護的 HLS 串流中，用於線上 (Online) 播放和離線 (Offline) 播放的核心媒體內容（加密的媒體片段，如 .ts 或 .fmp4 檔案）實質上**沒有差異**。

*   **加密方式與內容一致性**：
    *   兩者均採用相同的 **SAMPLE-AES (AES-128 CBCS)** 加密方式。
    *   編碼器對媒體片段的加密方式和使用的內容金鑰 (Content Key) 通常是相同的。
    *   因此，同一個加密後的媒體片段檔案可以同時用於線上即時串流和下載後供離線使用。編碼階段不需要為離線播放輸出不同格式的 TS 檔案或改變加密方法。

*   **金鑰**：
    *   內容加密金鑰由內容打包器 (Packager) 或編碼器產生（或從金鑰管理系統 KMS 獲取），並用於加密媒體片段。這個金鑰本身與播放是線上還是離線無關。

主要的差異不在於媒體檔案本身，而是在於 HLS 播放列表 (Manifest) 的結構以及授權伺服器發放的授權 (License) 中的策略。

### 用於 Offline HLS FairPlay 的 Manifest 內容

為了支援離線播放，HLS 的主播放列表 (Master Playlist) 需要進行特定的標記，以告知播放器哪些金鑰可用於離線，以及如何獲取這些金鑰的持久授權。

*   **`#EXT-X-SESSION-KEY` 標籤 (針對離線)**：
    *   根據 Apple 的離線 HLS 規範，主播放列表中**必須**加入 `#EXT-X-SESSION-KEY` 標籤，用以宣告所有可供離線使用的內容金鑰。
    *   此標籤的格式與媒體播放列表 (Variant Playlist) 中的 `#EXT-X-KEY` 標籤類似。
    *   屬性範例：
        ```m3u8
        #EXT-X-SESSION-KEY:METHOD=SAMPLE-AES,URI="skd://ASSET_ID",KEYFORMAT="com.apple.streamingkeydelivery",KEYFORMATVERSIONS="1"
        ```
    *   `METHOD=SAMPLE-AES`：指定加密方法。
    *   `URI="skd://ASSET_ID"`：指向 FairPlay 授權伺服器 (SKD Server)。`ASSET_ID` 通常是資源的唯一識別碼，對應後端儲存的持久化內容金鑰。播放器會使用此 URI 來請求持久授權。
    *   `KEYFORMAT="com.apple.streamingkeydelivery"`：標識 FairPlay DRM。 (在 `gemini.md` 中也提及 `com.apple.fairplay`，但 `com.apple.streamingkeydelivery` 更常與 HLS FairPlay 的金鑰請求流程相關)。
    *   `KEYFORMATVERSIONS="1"`：標識金鑰格式版本。
    *   此標籤告訴播放器該資產的持久化內容金鑰位置。

*   **`#EXT-X-KEY` 標籤 (媒體播放列表)**：
    *   在各個媒體播放列表 (Variant Playlist) 中，仍然會包含 `#EXT-X-KEY` 標籤。
    *   其屬性（如 `METHOD`, `URI`, `KEYFORMAT`）應與主播放列表中的 `#EXT-X-SESSION-KEY` 保持一致，指向相同的解密金鑰。
    *   對於離線播放，此標籤中的 URI 在下載內容時被用於請求持久授權。

*   **初始化向量 (IV)**：
    *   線上與離線播放使用相同的 IV 方案，如需指定則在 `#EXT-X-KEY` 或 `#EXT-X-SESSION-KEY` 標籤中設定。

*   **EXT-X-FAXS-CM 標籤**：
    *   此標籤屬於 Adobe FlashAccess (AAXS) 的 HLS DRM 擴展，FairPlay 不使用此標籤。

總結來說，編碼器輸出的媒體片段對於線上和離線是相同的。主要的修改在於主播放列表中加入 `#EXT-X-SESSION-KEY` 標籤，以明確支援離線金鑰的聲明。

## Player 請求 DRM license 階段

離線與在線播放的主要差異體現在播放器如何處理金鑰以及與授權伺服器的互動，特別是在授權的獲取和儲存方式上。

### License Server 與 Player 的互動

#### 針對 Offline HLS Fairplay 資訊的 License Request 如何進行

1.  **啟動下載與金鑰預載**：
    *   應用程式通常使用 `AVAssetDownloadTask` 來下載 HLS 資產以供離線播放。
    *   在下載開始前，播放器（應用程式）需要獲取並持久化儲存 FairPlay 金鑰。
    *   Apple 建議將 `AVAssetResourceLoader.preloadsEligibleContentKeys` 設為 `true`，這樣 `AVURLAsset` 會在下載任務啟動前預先載入所有相關的持久金鑰。

2.  **請求持久授權 (Persistent License)**：
    *   播放器解析主播放列表，找到 `#EXT-X-SESSION-KEY` 標籤（或媒體播放列表中的 `#EXT-X-KEY` 標籤）。
    *   透過 `AVContentKeySession`，使用標籤中的 URI 向 FairPlay 授權伺服器 (License Server / SKD Server) 發起請求，以獲取一個**可持續性授權 (Persistent License)**。
    *   此請求通常包含由播放器產生的伺服器播放上下文 (Server Playback Context, SPC)。

3.  **授權伺服器處理**：
    *   授權伺服器接收到 SPC。
    *   驗證請求的合法性。
    *   根據 `ASSET_ID`（或其他識別碼）查找對應的內容金鑰。
    *   檢查並應用針對離線播放的特定授權策略（見下方「License Server 的實作重點」）。
    *   產生包含內容金鑰和離線策略的內容金鑰上下文 (Content Key Context, CKC)。此 CKC 必須是持久類型的。

4.  **儲存持久授權**：
    *   播放器接收到 CKC。
    *   將此持久化的 CKC（包含內容金鑰）安全地儲存在裝置本機上。

5.  **下載媒體內容**：
    *   在成功獲取並儲存持久授權後，播放器開始下載加密的媒體片段。

6.  **離線播放時的金鑰提供**：
    *   當使用者在離線狀態下播放已下載的內容時，播放器需要解密金鑰。
    *   此時，應用程式的 `AVContentKeySessionDelegate` 會攔截金鑰請求。
    *   `AVContentKeySessionDelegate` 使用先前儲存在本機的持久性金鑰來回應請求，而不會（也不能）再次向網路上的授權伺服器發起請求。

#### Sequence Diagram (序列圖)

```mermaid
sequenceDiagram
    participant PlayerApp as Player (Application)
    participant AVFCore as AVFoundation Core (iOS/tvOS)
    participant LicenseServer as FairPlay License Server (SKD)

    PlayerApp->>AVFCore: Initiate Offline Download (AVAssetDownloadTask) for HLS Asset
    AVFCore-->>PlayerApp: Parse Master Playlist (e.g., finds #EXT-X-SESSION-KEY)
    Note over PlayerApp,AVFCore: preloadsEligibleContentKeys = true
    PlayerApp->>AVFCore: Request Persistent Key (via AVContentKeySession, using URI from manifest)
    AVFCore->>LicenseServer: Send SPC (Server Playback Context) for Persistent License
    LicenseServer->>LicenseServer: Validate SPC, Retrieve Content Key, Apply Offline Policies
    LicenseServer-->>AVFCore: Return Persistent CKC (Content Key Context)
    AVFCore-->>PlayerApp: Provide CKC
    PlayerApp->>PlayerApp: Securely Store Persistent Key from CKC
    PlayerApp->>AVFCore: Start Downloading Encrypted Media Segments

    %% Later, during offline playback %%
    Note over PlayerApp,AVFCore: Offline Playback Initiated
    AVFCore->>PlayerApp: Request Content Key (via AVContentKeySessionDelegate)
    PlayerApp->>PlayerApp: Retrieve Stored Persistent Key
    PlayerApp-->>AVFCore: Provide Stored Persistent Key
    AVFCore->>AVFCore: Decrypt and Play Content
```

#### License Server 的實作重點

授權伺服器在支援離線播放時，扮演著關鍵角色，主要體現在發放的授權類型和策略上：

*   **支援持久授權 (Persistent License)**：
    *   必須能夠發放可持續性授權，這種類型的授權在獲取後會被安全地儲存在裝置本機上。
    *   線上播放通常使用非持續性 (Non-persistent) 或臨時授權 (Temporary License)，其生命週期與當前播放會話綁定。

*   **定義離線播放策略**：
    *   授權伺服器需要在發放的持久授權中嵌入特定的離線播放策略。這些策略控制著內容在離線狀態下的使用方式，例如：
        *   **租賃期 (Rental Duration / Storage Duration)**：授權下載後，在首次播放前允許儲存的有效時間。
        *   **播放期 (Playback Duration)**：內容首次播放後，可以被播放的總時長。
        *   **絕對到期日 (Absolute Expiry)**：一個固定的日期和時間，之後授權將失效，無論是否播放過或播放時長如何。
    *   這些策略由授權伺服器根據業務邏輯在產生 CKC 時設定。

*   **金鑰管理**：
    *   需要安全地管理與 `ASSET_ID` 對應的內容金鑰，並確保這些金鑰可以被用於產生持久授權。

#### Player 的實作範例 (重點概念)

播放器端的實作主要圍繞 Apple 的 `AVFoundation` 框架：

*   **`AVAssetDownloadTask`**：用於下載 HLS 串流以供離線播放。
*   **`AVContentKeySession`**：核心物件，用於處理 DRM 金鑰請求的生命週期。
    *   與 `AVContentKeySessionDelegate` 協同工作。
*   **`AVContentKeySessionDelegate`**：
    *   `contentKeySession(_:didProvide:)` 系列方法：處理從授權伺服器收到的金鑰響應 (CKC)。
    *   `contentKeySession(_:shouldRetry:)`：處理金鑰請求失敗和重試邏輯。
    *   在離線播放時，此代理負責從本地儲存中提供先前獲取的持久金鑰。
*   **`AVAssetResourceLoaderDelegate`**：
    *   `resourceLoader(_:shouldWaitForLoadingOfRequestedResource:)`：可以攔截金鑰請求。
    *   `preloadsEligibleContentKeys` 屬性設為 `true`，允許 `AVURLAsset` 在下載任務啟動前預先載入所有相關的持久金鑰。
*   **持久金鑰的儲存與管理**：
    *   應用程式需要安全地儲存從授權伺服器獲取的持久化內容金鑰。通常儲存在應用程式的沙盒內受保護的區域。
    *   需要管理這些已儲存金鑰的生命週期，例如根據授權策略（如到期日）進行刪除。

透過上述機制，播放器能夠在有網路連接時下載內容和對應的持久授權，並在之後無網路連接的環境下，使用本地儲存的授權來解密和播放內容。