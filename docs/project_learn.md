# KKTV DRM服務器分析

本文檔提供對KKTV DRM服務器項目的全面分析，特別關注FairPlay DRM實現。

## 目錄

1. [HTTP API端點分析](#http-api端點分析)
2. [模組架構](#模組架構)
3. [授權流程](#授權流程)
4. [FairPlay實現詳解](#fairplay實現詳解)
5. [架構圖](#架構圖)

## HTTP API端點分析

DRM服務器在`index.js`中定義了以下主要API端點：

### 1. `/status` (GET)

- **職責**: 提供服務健康檢查
- **響應**: 返回HTTP 200狀態碼
- **用途**: 用於監控和負載均衡檢查

### 2. CORS預檢請求處理 (OPTIONS)

- **職責**: 處理瀏覽器的CORS預檢請求
- **響應**: 返回適當的CORS標頭
- **用途**: 支持從不同域名的網頁訪問DRM服務

### 3. DRM證書獲取 (GET)

- **路徑模式**: `/:drm_name_cert(_base64)?`
- **示例**: `/fairplay_cert` 或 `/fairplay_cert_base64`
- **職責**: 提供DRM客戶端所需的服務器證書
- **參數**:
  - `drm_name`: DRM系統名稱(如fairplay)
  - 可選後綴`_base64`: 指示是否以base64格式返回(適用於Apple TV)
- **處理邏輯**:
  - 調用對應DRM插件的`getServerCertificate()`方法
  - 根據請求格式(是否為base64)處理輸出

### 4. DRM授權請求處理 (POST)

- **職責**: 處理客戶端DRM授權請求，返回許可證
- **處理流程**:
  1. 解析客戶端請求
  2. 從請求中獲取token或自定義數據
  3. 嘗試匹配適當的DRM解決方案
  4. 驗證token權限
  5. 獲取內容密鑰
  6. 生成授權回應
  7. 傳輸授權給客戶端

## 模組架構

DRM服務器採用模塊化架構，通過插件系統支持多種DRM技術：

### 核心組件

1. **主服務器模塊** (`index.js`)
   - 提供HTTP伺服器和API端點
   - 處理請求路由和響應
   - 集成各DRM插件

2. **伺服器配置和啓動** (`drm_server.js`)
   - 設定服務器配置
   - 註冊DRM插件
   - 管理進程和訊號處理

### DRM插件

1. **FairPlay插件** (`drm-plugin-fairplay/`)
   - 處理Apple FairPlay DRM請求
   - 包含JavaScript介面和C++原生實現

2. **Widevine插件** (`drm-plugin-widevine/`)
   - 處理Google Widevine DRM請求
   - 實現與Widevine服務器的通信

3. **PlayReady插件** (`drm-plugin-playready/`)
   - 處理Microsoft PlayReady DRM請求
   - 實現XML格式的請求處理和響應

4. **測試插件** (`drm-plugin-for-test/`)
   - 提供測試環境使用的簡化DRM實現

### 插件API標準

所有DRM插件需實現統一的接口：

- **初始化方法**: 接受通用配置和插件特定配置
- **`requestMatch()`**: 檢查請求是否與此DRM系統匹配
- **`getServerCertificate()`**: 提供DRM證書(如適用)
- **`onLicenseAcquire()`**: 生成許可證響應
- **`releaseLicenseObject()`**: 清理資源

## 授權流程

DRM授權過程涉及以下步驟：

1. **請求匹配**
   - 客戶端提交請求
   - 服務器通過`tryDRMSolutions()`方法嘗試各註冊插件
   - 匹配的插件解析請求並提取內容密鑰ID

2. **權限驗證**
   - 支持三種token類型:
     - **null token**: 無需驗證(僅限開發環境)
     - **vendor token**: 預定義的供應商令牌
     - **playback token**: 通過授權服務器驗證的播放令牌

3. **密鑰獲取**
   - 從密鑰服務器或預設密鑰配置中獲取內容解密密鑰
   - 視需要獲取初始化向量(IV)

4. **許可證生成**
   - 將內容密鑰和過期時間傳遞給DRM插件
   - 插件生成符合特定DRM格式的許可證

5. **許可證傳遞**
   - 添加HTTP標頭(包括CORS和緩存控制)
   - 支持壓縮(gzip或deflate)
   - 將許可證發送給客戶端

## FairPlay實現詳解

Apple FairPlay是一個完整的DRM實現，結合了JavaScript介面和C++實現:

### 架構組件

1. **JavaScript介面** (`drm-plugin-fairplay.js`)
   - 提供與核心服務器的整合點
   - 實現插件API要求的方法
   - 處理輸入/輸出格式轉換

2. **原生實現** (`fpssdk.cpp` 和相關C文件)
   - 實現FairPlay協議的加密和解密操作
   - 處理SPC(Server Playback Context)解析
   - 生成CKC(Content Key Context)

### FairPlay授權流程

1. **初始化**
   - 設置私鑰、ASK(Application Secret Key)和服務器證書

2. **證書請求處理**
   - 客戶端首先請求FairPlay服務器證書
   - 服務器通過`getServerCertificate()`提供證書

3. **SPC處理**
   - 解析客戶端提供的SPC請求
   - SPC支持兩種格式:
     - 原始二進制格式(開頭為`[0,0,0,1]`)
     - URL編碼格式(以`spc=AA...`開頭)
   - 提取assetId，分解為keyId和IvId

4. **密鑰應用**
   - 將內容密鑰和IV應用到FPS數據結構
   - 設置許可證過期時間

5. **許可證生成**
   - 調用C++原生方法`GenerateLicense()`
   - 根據客戶端需求格式化輸出(RAW或XML)

### 關鍵安全機制

FairPlay實現包含多層安全機制：

- **證書驗證**: 使用Apple提供的證書建立信任
- **密鑰保護**: 內容密鑰經過多層加密保護
- **租期控制**: 通過過期時間限制許可證有效期
- **資產綁定**: 許可證與特定內容ID綁定

## 架構圖

### DRM服務器整體架構

```
┌─────────────────────────────────────┐
│           客戶端應用程序            │
│  (iOS/Android/Web播放器/AppleTV)    │
└───────────────┬─────────────────────┘
                │
                ▼
┌─────────────────────────────────────┐
│          HTTP API 端點              │
│  /status                            │
│  /:drm_name_cert(_base64)?          │
│  POST (授權請求)                    │
└───────────────┬─────────────────────┘
                │
                ▼
┌─────────────────────────────────────┐
│          DRM插件註冊系統            │
└──┬─────────────┬────────────┬───────┘
   │             │            │
   ▼             ▼            ▼
┌─────────┐ ┌─────────┐ ┌─────────────┐
│ FairPlay │ │Widevine │ │ PlayReady   │
└────┬─────┘ └────┬────┘ └──────┬──────┘
     │            │             │
     ▼            ▼             ▼
┌─────────────────────────────────────┐
│         權限驗證與密鑰服務          │
└───────────────┬─────────────────────┘
                │
                ▼
┌─────────────────────────────────────┐
│          外部服務集成               │
│  - 密鑰服務器                       │
│  - 授權服務器                       │
└─────────────────────────────────────┘
```

### FairPlay授權流程

```
┌───────────────┐    1. 請求證書     ┌───────────────┐
│               │─────────────────>  │               │
│  客戶端設備   │                    │  DRM服務器    │
│  (iOS/tvOS)   │ <─────────────────│               │
└───────┬───────┘    2. 返回證書     └───────┬───────┘
        │                                    │
        │ 3. 生成SPC                         │
        ▼                                    │
┌───────────────┐    4. 發送SPC請求    ┌───────────────┐
│               │─────────────────────>│               │
│  客戶端設備   │                      │  DRM服務器    │
│  (iOS/tvOS)   │                      │               │
└───────────────┘                      └───────┬───────┘
                                               │
                                               │ 5. 解析SPC
                                               ▼
                                       ┌───────────────┐
                                       │ 提取keyId/IvId│
                                       └───────┬───────┘
                                               │
                                               │ 6. 權限驗證
                                               ▼
                         ┌───────────┐ ┌───────────────┐
                         │授權服務器 │<│ 驗證token      │
                         └───────────┘ └───────┬───────┘
                                               │
                                               │ 7. 請求密鑰
                                               ▼
                         ┌───────────┐ ┌───────────────┐
                         │密鑰服務器 │<│ 獲取內容密鑰  │
                         └───────────┘ └───────┬───────┘
                                               │
                                               │ 8. 生成許可證
                                               ▼
┌───────────────┐    9. 返回CKC許可證   ┌───────────────┐
│               │<────────────────────  │               │
│  客戶端設備   │                       │  DRM服務器    │
│  (iOS/tvOS)   │                       │               │
└───────────────┘                       └───────────────┘
```

### FairPlay模塊關係

```
┌─────────────────────────────┐
│  drm-plugin-fairplay.js     │
│                             │
│  - 插件API實現              │
│  - 請求匹配與處理           │
│  - 許可證生成流程           │
└──────────────┬──────────────┘
               │
               ▼
┌─────────────────────────────┐
│fairplaystreaming.FPS (C++模塊)
│                             │
│  - SPC解析與驗證            │
│  - 加密操作                 │
│  - CKC生成                  │
└──────────────┬──────────────┘
               │
               ▼
┌─────────────────────────────┐
│  SKDServer.c, SKDDef.h 等   │
│                             │
│  - 底層FairPlay協議實現     │
│  - 密碼學操作               │
└─────────────────────────────┘
```

## 結論

KKTV DRM服務器是一個多功能的DRM解決方案，支持主流的三種DRM系統(FairPlay、Widevine和PlayReady)。其模塊化設計允許靈活擴展和維護。FairPlay實現特別完善，結合JavaScript和C++原生代碼，提供了高效且安全的蘋果設備DRM授權流程。

服務器設計的主要優點包括:
1. 統一的插件接口簡化新DRM系統整合
2. 靈活的令牌驗證和授權機制
3. 與外部密鑰管理系統的整合
4. 全面的錯誤處理和日誌記錄