FROM ubuntu:14.04

# 設置非互動模式，避免安裝過程中的提示
ENV DEBIAN_FRONTEND=noninteractive

# 更新套件列表並安裝 Ansible 運行所需的基本依賴
RUN apt-get update && apt-get install -y \
    python-pip \
    ansible \
    git \
    wget \
    curl \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# 創建必要的目錄結構（模擬 CodeDeploy 的目錄結構）
RUN mkdir -p /opt/codedeploy-agent/deployment-root/
RUN mkdir -p /kkcorp/
RUN mkdir -p /var/log/

# 設置工作目錄
WORKDIR /app

# 複製項目文件
COPY . .

# 設置腳本權限
RUN find /app -name "*.sh" -type f -exec chmod +x {} \;

# 暴露端口（根據您的應用需求）
EXPOSE 8000

# 保持容器運行
CMD ["tail", "-f", "/dev/null"]