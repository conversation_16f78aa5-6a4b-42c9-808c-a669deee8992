version: ~> 1.0
import:
  - KKTV/kktv-shared-configs:travis-ci/notification.yml@main

language: nodejs
python:
  - "3.7"
sudo: true
before_install:
  - export PROJECT_NAME=`echo $TRAVIS_REPO_SLUG|cut -d'/' -f2`
  - export EXPIRE_TIME=`date --date="next hour" +"%Y-%m-%d %T %z"`
  - export CURRENT_TIME=`date +"%Y%m%d-%H%M%S"`
  - export PACKAGE_NAME=$PROJECT_NAME-$TRAVIS_COMMIT
  - export AWS_DEFAULT_REGION=ap-northeast-1
  - case $TRAVIS_BRANCH in prod) export ALIAS=prod;; stag) export ALIAS=stag;; master) export ALIAS=test;; *) export ALIAS=test;; esac;
install:
  - ls
script:
  - ls
after_success:
  - zip -r codedeploy.zip *
  - mkdir -p packages/$PACKAGE_NAME
  - echo $PACKAGE_NAME > packages/$PROJECT_NAME.$ALIAS
  - mv codedeploy.zip packages/$PACKAGE_NAME/codedeploy.zip
deploy:
  - provider: script
    script: >-
      sudo pip3 install 'awscli<1.19';
      aws s3 mv --recursive packages/$PACKAGE_NAME s3://kktv-packages/$PROJECT_NAME/$PACKAGE_NAME/;
      aws s3 mv packages/$PROJECT_NAME.$ALIAS s3://kktv-packages/$PROJECT_NAME/;
    skip_cleanup: true
    on:
      all_branches: true
      condition: $TRAVIS_BRANCH =~ ^(develop|master|prod)$
  - provider: script
    script: >-
      aws deploy create-deployment --application-name kktv-$ALIAS-drm --deployment-config-name CodeDeployDefault.OneAtATime --deployment-group-name kktv-$ALIAS-drm --s3-location bucket=kktv-packages,bundleType=zip,key=$PROJECT_NAME/$PACKAGE_NAME/codedeploy.zip
    on:
      all_branches: true
      condition: $TRAVIS_BRANCH =~ ^(develop|master|prod)$
