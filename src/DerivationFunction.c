/*
 Copyright (C) 2015 Apple Inc. All Rights Reserved.
 See the Apple Developer Program License Agreement for this file's licensing information.
 All use of these materials is subject to the terms of the Apple Developer Program License Agreement.
 
 Abstract:
 Reference implementation of the D Function as explained in the D Function Guide.
 */

#include <string.h>
#include <stdio.h>
#include <stdlib.h>

#include "PlatformTypes.h"
#include "SKDServerD.h"

#include "SKDServer.h"
#include "SKDServerUtils.h"

#include <openssl/sha.h>

#define PRIME ((UInt32) 813416437UL)
#define NB_RD 16

int isForceOverwriteDASk = 0;
UInt8 ForceOverwriteDASk[PS_AES128_KEY_SZ];

OSStatus DFunction(
        UInt8  *R2,             /* in */
        UInt32  R2_sz,          /* in */
const   UInt8   ASk[16],        /* in */
        UInt8   DASk[16]        /* out */
        )
{
  if(isForceOverwriteDASk) {
    OSStatus status = noErr;
    memcpy(DASk, ForceOverwriteDASk, PS_AES128_KEY_SZ);
    return status;
  } else {
    OSStatus status = noErr;
    SHA_CTX HCTx;
    UInt8 tmp[20];
    UInt8 pad[64];
    UInt32 i, MBlock[14], r, rc = 0;
    UInt32 P = PRIME;

    PS_RequireAction( R2_sz < 56, return kDRMSKDServerParamErr; );

    /* Padding until a multiple of 56B */
    for (i = 0; i < R2_sz; i++) {
        pad[i] = R2[i];
    }
    pad[R2_sz] = 0x80;
    for (i = R2_sz + 1; i < 56; i++) {
        pad[i] = 0;
    }

    /* Create 14 32b values */
    for (i = 0 ; i < 14; i++) {
        MBlock[i] = (pad[4 * i] << 24) ^ (pad[4 * i + 1] << 16) ^ (pad[4 * i + 2] << 8) ^ (pad[4 * i + 3]);
    }

    /* Reunify into 2 32 bits values */
    for (i = 1 ; i < 7; i++) {
        MBlock[0] += MBlock[i];
    }
    MBlock[1] = 0;
    for (i = 0 ; i < 7; i++) {
        MBlock[1] += MBlock[i + 7];
    }
    
    /* Apply the function */
    /* This block is the C_r function specified in the D Function specification document */
    for (i = 0 ; i < 2; i++) {
        for (r = 0; r < NB_RD; r++) {
            if (MBlock[i] & 1) {
                MBlock[i] >>= 1;
            } else {
                MBlock[i] = (3 * MBlock[i] + 1) % P;
            }
        }
    }

    /* append to M */
    for (i = 0 ; i < 4; i++) {
        pad[56 + i] = (MBlock[0] >> (8 * i));
        pad[60 + i] = (MBlock[1] >> (8 * i));
    }

    /* Hash R2 */
    rc = SHA1_Init(&HCTx);
    if (1 == rc)
        rc = SHA1_Update(&HCTx, pad, 64);
        if (1 == rc)
            rc = SHA1_Final(tmp, &HCTx);

    if (rc == 1)
        status = SKDServerAESEncryptDecrypt(tmp, DASk, 16, (UInt8*)ASk, NULL, kSKDServerAESEncrypt, kSKDServerAES_ECB);
    else
        status = kDRMSKDServerOpenSSLErr;

    return status;
  }
}
