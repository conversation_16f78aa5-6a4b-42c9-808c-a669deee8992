#include "node_utils.h"

#include "PlatformTypes.h"
#include "SKDServer.h"
#include "SKDDef.h"
#include "SKDServerUtils.h"

extern UInt8 *pKeyPem;
extern UInt32 pKeyPemSize;
extern UInt8 pASKey[PS_AES128_KEY_SZ];
extern int isForceOverwriteDASk;
extern UInt8 ForceOverwriteDASk[PS_AES128_KEY_SZ];

extern "C" {
  void SKDServerDestroyCtx(SKDServerCtxV1 *serverCtx);
  OSStatus SKDServerParseSPCV1(const UInt8 *serverPlaybackCtx, UInt32 serverPlaybackCtxSize, SKDServerSPCContainerV1 *spcContainer);
  OSStatus SKDServerGenerateCKCV1(SKDServerCtxV1 *serverCtx, UInt8 **contentKeyCtx, UInt32 *contentKeyCtxSize);
};

class FPSSession: public node::ObjectWrap
{
  public:
    SKDServerCtxV1 serverCtx;
    OSStatus lastStatus;

    FPSSession() { memset(&serverCtx, 0, sizeof(serverCtx)); lastStatus = 0; }
    virtual ~FPSSession() { SKDServerDestroyCtx(&serverCtx); }

    static v8::Persistent<v8::Function> constructor;
    void ExtWrap(v8::Handle<v8::Object> handle) { Wrap(handle); }
};
v8::Persistent<v8::Function> FPSSession::constructor;

static void NodeNewFPSSession(ARGS)
{
  DECLEAR_NEW(FPSSession);
  if(!node::Buffer::HasInstance(args[0])) {
    node::ThrowTypeError(isolate, "Should be buffer");
    return;
  }

  const UInt8   *serverPlaybackCtx = (const UInt8 *) node::Buffer::Data(args[0]);
  UInt32         serverPlaybackCtxSize = node::Buffer::Length(args[0]);

  v8::Local<v8::Object> self = args.This();

  UInt8 localVersion[PS_V1_VERSION_SZ] = {0};
  OSStatus status = SKDServerReadBytes(NULL, PS_V1_VERSION_SZ, serverPlaybackCtx, serverPlaybackCtxSize, localVersion);

  if(!PS_IS_NO_ERROR(status)) {
    self->Set(toV8String("Error"), v8::Int32::New(isolate, status));
    node::ThrowTypeError(isolate, "Version Parse Error");
    return;
  }

  if(SKDServerGetBigEndian32(localVersion) != kServerPlaybackCtxV1) {
    node::ThrowTypeError(isolate, "Only support Version 1");
    return;
  }

  SKDServerCtxV1 serverCtx;
  memset(&serverCtx, 0, sizeof(SKDServerCtxV1));

  // 1. parse SPC
  status = SKDServerParseSPCV1(serverPlaybackCtx, serverPlaybackCtxSize, &serverCtx.spcContainer);
  if(!PS_IS_NO_ERROR(status)) {
    self->Set(toV8String("Error"), v8::Int32::New(isolate, status));
    node::ThrowTypeError(isolate, "Parse SPC Error");
    SKDServerDestroyCtx(&serverCtx);
    return;
  }

#define	SETBUFFER(k,v)	spcContainer->Set(toV8String(k), toNodeBuffer((const char *) (v), sizeof(v)))

  v8::Local<v8::Object> spcContainer = v8::Object::New(isolate);

  spcContainer->Set(toV8String("Version"), v8::Uint32::New(isolate, serverCtx.spcContainer.version));
  SETBUFFER("aesKeyIV", serverCtx.spcContainer.aesKeyIV);
  SETBUFFER("aesWrappedKey", serverCtx.spcContainer.aesWrappedKey);
  SETBUFFER("certificateHash", serverCtx.spcContainer.certificateHash);
  spcContainer->Set(toV8String("spcDecryptedData"), toNodeBuffer((const char *) (serverCtx.spcContainer.spcDecryptedData), serverCtx.spcContainer.spcDataSize));
  spcContainer->Set(toV8String("spcDataOffset"), v8::Uint32::New(isolate, serverCtx.spcContainer.spcDataOffset));

#undef	SETBUFFER
#define	SETBUFFER(k,v)	spcData->Set(toV8String(k), toNodeBuffer((const char *) (v), sizeof(v)))
  v8::Local<v8::Object> spcData = v8::Object::New(isolate);
  SETBUFFER("antiReplaySeed", serverCtx.spcContainer.spcData.antiReplaySeed);
  SETBUFFER("DAS_k", serverCtx.spcContainer.spcData.DAS_k);
  SETBUFFER("sk", serverCtx.spcContainer.spcData.sk);
  SETBUFFER("hu", serverCtx.spcContainer.spcData.hu);
  SETBUFFER("r2", serverCtx.spcContainer.spcData.r2);
  SETBUFFER("r1", serverCtx.spcContainer.spcData.r1);
  SETBUFFER("sk_r1_integrity_tag", serverCtx.spcContainer.spcData.sk_r1_integrity_tag);
  SETBUFFER("sk_r1_integrity", serverCtx.spcContainer.spcData.sk_r1_integrity);
  SETBUFFER("sk_r1", serverCtx.spcContainer.spcData.sk_r1);
  spcData->Set(toV8String("assetId"), toNodeBuffer((const char *) serverCtx.spcContainer.spcData.assetId, serverCtx.spcContainer.spcData.assetIdSize));
  spcData->Set(toV8String("p_version_used"), v8::Uint32::New(isolate, serverCtx.spcContainer.spcData.p_version_used));
  spcData->Set(toV8String("whereToPlay"), v8::Uint32::New(isolate, serverCtx.spcContainer.spcData.whereToPlay));

  v8::Local<v8::Object> returnTLLVs = v8::Object::New(isolate);
  for(int i = 0; i < serverCtx.spcContainer.spcData.nbReturnTLLVs; i++) {
    char buf[17];
    sprintf(buf, "%02x%02x%02x%02x%02x%02x%02x%02x",
	(serverCtx.spcContainer.spcData.returnTLLVs[i].valueData - PS_TLLV_HEADER_SZ)[0],
	(serverCtx.spcContainer.spcData.returnTLLVs[i].valueData - PS_TLLV_HEADER_SZ)[1],
	(serverCtx.spcContainer.spcData.returnTLLVs[i].valueData - PS_TLLV_HEADER_SZ)[2],
	(serverCtx.spcContainer.spcData.returnTLLVs[i].valueData - PS_TLLV_HEADER_SZ)[3],
	(serverCtx.spcContainer.spcData.returnTLLVs[i].valueData - PS_TLLV_HEADER_SZ)[4],
	(serverCtx.spcContainer.spcData.returnTLLVs[i].valueData - PS_TLLV_HEADER_SZ)[5],
	(serverCtx.spcContainer.spcData.returnTLLVs[i].valueData - PS_TLLV_HEADER_SZ)[6],
	(serverCtx.spcContainer.spcData.returnTLLVs[i].valueData - PS_TLLV_HEADER_SZ)[7]);

    returnTLLVs->Set(toV8String(buf), toNodeBuffer((const char *) serverCtx.spcContainer.spcData.returnTLLVs[i].valueData, serverCtx.spcContainer.spcData.returnTLLVs[i].valueSize));
  }

  spcContainer->Set(toV8String("spcData"), spcData);
  spcContainer->Set(toV8String("returnTLLVs"), returnTLLVs);
  self->Set(toV8String("spcContainer"), spcContainer);

  // Page 36, table 5-2
  if((serverCtx.spcContainer.spcData.spcDataParser.presenceFlags & FLAG_TAG_CLIENT_REF_TIME) != 0) {
    int PlayerState = -1;
    switch(serverCtx.spcContainer.spcData.playInfo.playback) {
      case kFirstPlaybackCKRequired:
	PlayerState = 1;
	break;
      case kCurrentlyPlayingCKNotRequired:
	PlayerState = 2;
	break;
      case kCurrentlyPlayingCKRequired:
	PlayerState = 3;
	break;
      case kPlaybackSessionStopped:
	PlayerState = 4;
	break;
    }

    self->Set(toV8String("PlaybackState"), v8::Int32::New(isolate, PlayerState));
    if(PlayerState == -1) {
      node::ThrowTypeError(isolate, "playback type error");
      SKDServerDestroyCtx(&serverCtx);
      return;
    }

    self->Set(toV8String("PlayDate"), v8::Uint32::New(isolate, serverCtx.spcContainer.spcData.playInfo.date));
    self->Set(toV8String("PlaybackId"), v8::Integer::New(isolate, serverCtx.spcContainer.spcData.playInfo.playbackId));
  }

  FPSSession* fps = new FPSSession;
  memcpy(&fps->serverCtx, &serverCtx, sizeof(serverCtx));

  fps->ExtWrap(args.This());
  args.This()->Set(toV8String("assetId"), toV8String((const char *) serverCtx.spcContainer.spcData.assetId, (size_t) serverCtx.spcContainer.spcData.assetIdSize));

  RETURN(args.This());
}

static void FPSGenerateLicense(ARGS)
{
  DECLEARISOLATE;
  v8::HandleScope scope(isolate);

  v8::Local<v8::Object> self = args.This();
  FPSSession *session = node::ObjectWrap::Unwrap<FPSSession>(args.Holder());
  session->serverCtx.ckcContainer.regenreate_aes_iv = 1;

  FOREACH_OBJ(self, key, val, {
      if(key == "key") {
        copy_v8obj_to_mem(val, session->serverCtx.ckcContainer.ckcData.ck, sizeof(session->serverCtx.ckcContainer.ckcData.ck));
      } else if(key == "iv") {
        copy_v8obj_to_mem(val, session->serverCtx.ckcContainer.ckcData.iv, sizeof(session->serverCtx.ckcContainer.ckcData.iv));
      } else if(key == "leaseDuration") {
        session->serverCtx.ckcContainer.ckcData.keyDuration.leaseDuration = val->Uint32Value();
      } else if(key == "rentalDuration") {
        session->serverCtx.ckcContainer.ckcData.keyDuration.rentalDuration = val->Uint32Value();
      } else if(key == "ckcontainer_IV") {
        session->serverCtx.ckcContainer.regenreate_aes_iv = 0;
        copy_v8obj_to_mem(val, session->serverCtx.ckcContainer.aesKeyIV, sizeof(session->serverCtx.ckcContainer.aesKeyIV));
      } else if(key == "forceRandomNumber") {
        extern uint32_t forceNoRandomForTest;
        forceNoRandomForTest = val->Uint32Value();
      }
    });

  session->serverCtx.ckcContainer.ckcData.keyDuration.reserved = 0x86d34a3a;
  if(session->serverCtx.ckcContainer.ckcData.keyDuration.leaseDuration == 0) {
    if(session->serverCtx.ckcContainer.ckcData.keyDuration.rentalDuration != 0) {
      session->serverCtx.ckcContainer.ckcData.keyDuration.keyType = 0x3dfe45a0;
    }
  } else if(session->serverCtx.ckcContainer.ckcData.keyDuration.rentalDuration != 0) {
    session->serverCtx.ckcContainer.ckcData.keyDuration.keyType = 0x27b59bde;
  } else {
    session->serverCtx.ckcContainer.ckcData.keyDuration.keyType = 0x1a4bde7e;
  }

  UInt8 *contentKeyCtx = 0;
  UInt32 contentKeyCtxSize;
  OSStatus status = SKDServerGenerateCKCV1(&session->serverCtx, &contentKeyCtx, &contentKeyCtxSize);

  if(!PS_IS_NO_ERROR(status)) {
    self->Set(toV8String("Error"), v8::Int32::New(isolate, status));
    node::ThrowTypeError(isolate, "GenerateLicense Error");
    return;
  }

  {
    SKDServerCKCContainerV1 *ckcContainer = &session->serverCtx.ckcContainer;
    v8::Local<v8::Object> child = v8::Object::New(isolate);
    v8::Local<v8::Object> child2;

    child->Set(toV8String("Version"), v8::Uint32::New(isolate, ckcContainer->version));
    child->Set(toV8String("aesKeyIV"), toNodeBuffer((const char *) ckcContainer->aesKeyIV, sizeof(ckcContainer->aesKeyIV)));
    child->Set(toV8String("ckcData"), toNodeBuffer((const char *) ckcContainer->ckcDataPtr, ckcContainer->ckcDataSize));

    {
      SKDServerCKCDataV1 *ckcData = &ckcContainer->ckcData;
      v8::Local<v8::Object> child = v8::Object::New(isolate);
      child2 = child;

      child->Set(toV8String("tag_ck_TotalSize"), v8::Uint32::New(isolate, ckcData->tag_ck_TotalSize));
      child->Set(toV8String("orig_ck"), toNodeBuffer((const char *) ckcData->orig_ck, sizeof(ckcData->orig_ck)));

      child->Set(toV8String("ck"), toNodeBuffer((const char *) ckcData->ck, sizeof(ckcData->ck)));
      child->Set(toV8String("iv"), toNodeBuffer((const char *) ckcData->iv, sizeof(ckcData->iv)));
      child->Set(toV8String("r1"), toNodeBuffer((const char *) ckcData->r1, sizeof(ckcData->r1)));
      child->Set(toV8String("tag_r1_TotalSize"), v8::Uint32::New(isolate, ckcData->tag_r1_TotalSize));
    }
    child->Set(toV8String("ckcData"), child2);
    self->Set(toV8String("ckcContainer"), child);
  }

  RETURN(toNodeBuffer(contentKeyCtx, contentKeyCtxSize));
  SKDServerDisposeStorage(contentKeyCtx);
}

static void FPSSetPrivateKey(ARGS)
{
  DECLEARISOLATE;
  v8::HandleScope scope(isolate);

  if(args.Length() < 1) {
    node::ThrowTypeError(isolate, "must has pem key");
    return;
  }

  if(node::Buffer::HasInstance(args[0])) {
    pKeyPemSize = node::Buffer::Length(args[0]);
    if(pKeyPem)
      free(pKeyPem);
    pKeyPem = (UInt8 *) malloc(pKeyPemSize);
    memcpy(pKeyPem, node::Buffer::Data(args[0]), pKeyPemSize);
  } else {
    v8::String::Utf8Value str(args[0]);

    pKeyPemSize = str.length();
    if(pKeyPem)
      free(pKeyPem);
    pKeyPem = (UInt8 *) malloc(pKeyPemSize);
    memcpy(pKeyPem, *str, pKeyPemSize);
  }
}

static void FPSSetASK(ARGS)
{
  DECLEARISOLATE;
  v8::HandleScope scope(isolate);

  if(args.Length() < 1) {
    node::ThrowTypeError(isolate, "must has App Secert key");
    return;
  }

  isForceOverwriteDASk = 0;

  if(node::Buffer::HasInstance(args[0])) {
    size_t pASKeySize = node::Buffer::Length(args[0]);
    if(pASKeySize > PS_AES128_KEY_SZ)
      pASKeySize = PS_AES128_KEY_SZ;
    memcpy(pASKey, node::Buffer::Data(args[0]), pASKeySize);
  } else {
    v8::String::Utf8Value str(args[0]);
    size_t pASKeySize = str.length();
    if(pASKeySize > PS_AES128_KEY_SZ)
      pASKeySize = PS_AES128_KEY_SZ;
    memcpy(pASKey, *str, pASKeySize);
  }
}

static void FPSSetDASk(ARGS)
{
  DECLEARISOLATE;
  v8::HandleScope scope(isolate);

  if(args.Length() < 1) {
    node::ThrowTypeError(isolate, "must has Derivation App Secert key");
    return;
  }

  isForceOverwriteDASk = 1;

  if(node::Buffer::HasInstance(args[0])) {
    size_t pASKeySize = node::Buffer::Length(args[0]);
    if(pASKeySize > PS_AES128_KEY_SZ)
      pASKeySize = PS_AES128_KEY_SZ;
    memcpy(ForceOverwriteDASk, node::Buffer::Data(args[0]), pASKeySize);
  } else {
    v8::String::Utf8Value str(args[0]);
    size_t pASKeySize = str.length();
    if(pASKeySize > PS_AES128_KEY_SZ)
      pASKeySize = PS_AES128_KEY_SZ;
    memcpy(ForceOverwriteDASk, *str, pASKeySize);
  }
}

static void initialize(v8::Handle<v8::Object> exports) {
  DECLEAR_INIT(FPSSession, FPS, NodeNewFPSSession, 1);

  NODE_SET_METHOD(tpl, "SetPrivateKey", FPSSetPrivateKey);
  NODE_SET_METHOD(tpl, "SetASK", FPSSetASK);
  NODE_SET_METHOD(tpl, "SetDASk", FPSSetDASk);
  NODE_SET_PROTOTYPE_METHOD(tpl, "GenerateLicense", FPSGenerateLicense);

  DECLEAR_INIT_FIN(FPSSession, FPS, NodeNewFPSSession, 1);
  OBJ_SET(exports, "FPS", tpl->GetFunction());
}

NODE_MODULE(fairplaystreaming, initialize);
