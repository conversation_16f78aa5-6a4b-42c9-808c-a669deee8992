#ifndef _NODE_UTILS_H_
#define	_NODE_UTILS_H_

#include <node.h>
#include <node_buffer.h>
#include <node_object_wrap.h>
// #include <node_internals.h>

#include <string>
#include <cstring>

namespace node {
void ThrowTypeError(v8::Isolate* isolate, const char* errmsg);
}

static inline std::string toString(v8::Handle<v8::Value> val)
{
  if(node::Buffer::HasInstance(val)) {
    return std::string(node::Buffer::Data(val), node::Buffer::Length(val));
  } else {
    v8::String::Utf8Value str(val);
    return std::string(*str, str.length());
  }
}

static inline v8::Local<v8::String> toV8String(v8::Handle<v8::Value> val)
{
  return val->ToString();
}

static inline v8::Local<v8::String> toV8String(std::string& str)
{
  return v8::String::NewFromUtf8(v8::Isolate::GetCurrent(), (char *) str.c_str(), v8::String::kNormalString, str.size());
}

static inline v8::Local<v8::String> toV8String(const char *str)
{
  return v8::String::NewFromUtf8(v8::Isolate::GetCurrent(), (char *) str);
}

static inline v8::Local<v8::String> toV8String(const char *str, size_t length)
{
  return v8::String::NewFromUtf8(v8::Isolate::GetCurrent(), (char *) str, v8::String::kNormalString, length);
}

static inline v8::Local<v8::Object> toNodeBuffer(std::string& str)
{
  v8::Local<v8::Object> tmp;
  bool unused = node::Buffer::Copy(v8::Isolate::GetCurrent(), (char *) str.c_str(), (size_t) str.size()).ToLocal(&tmp);
  return tmp;
}

static inline int copy_v8obj_to_mem(v8::Handle<v8::Value> val, void *dst, int size)
{
  if(node::Buffer::HasInstance(val)) {
    if((int) node::Buffer::Length(val) < size)
      size = node::Buffer::Length(val);
    memcpy(dst, node::Buffer::Data(val), size);
  } else {
    v8::String::Utf8Value str(val);
    if(str.length() < size)
      size = str.length();
    memcpy(dst, *str, size);
  }
  return size;
}

template<class T>
static inline v8::Local<v8::Object> toNodeBuffer(T *ptr, size_t size)
{
  v8::Local<v8::Object> tmp;
  bool unused = node::Buffer::Copy(v8::Isolate::GetCurrent(), (char *) ptr, size).ToLocal(&tmp);
  return tmp;
}

#define	RETURN(ret)	args.GetReturnValue().Set(ret)
#define ARGS            const v8::FunctionCallbackInfo<v8::Value>& args
#define DECLEARISOLATE  v8::Isolate *isolate = v8::Isolate::GetCurrent()
#define	DECLEARCONTSTURCTOR static v8::Persistent<v8::Function> constructor

#define	FOREACH_OBJ(obj, name, value, ...) { \
  v8::Local<v8::Object> __obj = (obj)->ToObject(); \
  v8::Local<v8::Array> __obj_keys = __obj->GetOwnPropertyNames(); \
  uint32_t __key_index, __key_length = __obj_keys->Length(); \
  for(__key_index = 0; __key_index < __key_length; __key_index++) { \
    v8::Local<v8::String> __key_name = __obj_keys->Get(__key_index)->ToString(); \
    std::string name = toString(__key_name); \
    v8::Local<v8::Value> value = (obj)->Get(__key_name); \
    __VA_ARGS__ \
  } \
}

#define	DECLEAR_NEW(type) \
  DECLEARISOLATE; \
  v8::HandleScope scope(isolate); \
  if(!args.IsConstructCall()) { \
    const int argc = args.Length(); \
    v8::Local<v8::Value> argv[argc]; \
    int i; for(i = 0; i < argc; i++) argv[i] = args[i]; \
    v8::Local<v8::Function> cons = v8::Local<v8::Function>::New(isolate, type::constructor); \
    RETURN(cons->NewInstance(argc, argv)); \
    return; \
  }

#define	DECLEAR_INIT(classname, v8classname, init, internalFieldCounts) \
  DECLEARISOLATE; \
  v8::Local<v8::FunctionTemplate> tpl; \
  DECLEAR_INIT_MORE(classname, v8classname, init, internalFieldCounts)

#define DECLEAR_INIT_MORE(classname, v8classname, init, internalFieldCounts) \
  tpl = v8::FunctionTemplate::New(isolate, init); \
  tpl->SetClassName(toV8String(#v8classname)); \
  tpl->InstanceTemplate()->SetInternalFieldCount(internalFieldCounts); \

#define	DECLEAR_INIT_FIN(classname, v8classname, init, internalFieldCounts) \
  classname::constructor.Reset(isolate, tpl->GetFunction());

#define	OBJ_SET(obj, key, value)	obj->Set(toV8String(key), value)

/*
static int operator=(int &dst, v8::Handle<v8::Value> val) {
  dst = val->ToInt32()->Value();
  return dst;
}
static unsigned int operator=(unsigned int &dst, v8::Handle<v8::Value> val) {
  dst = val->ToUint32()->Value();
  return dst;
}
static std::string &operator=(std::string &dst, v8::Handle<v8::Value> val) {
  if (node::Buffer::HasInstance(val)) {
    dst.assign(node::Buffer::Data(val), node::Buffer::Length(val));
  } else {
    v8::String::Utf8Value str(key);
    dst.assign(*key, key.length());
  }
  return dst;
}
*/

#endif
