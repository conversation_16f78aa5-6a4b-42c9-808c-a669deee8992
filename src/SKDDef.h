#ifndef	_SKDDEF_
#define _SKDDEF_
typedef struct {
  UInt32 totalSize;
  UInt32 valueSize;
  UInt8 *valueData;
} SKDServerTLLV;

typedef struct {
  UInt8 scratch_4[4];
  UInt8 scratch_8[8];
  UInt32 currentOffset;
} SKDServerParser;

typedef struct {
  SKDServerParser parser;
  SKDServerTLLV *TLLVs;
  UInt32 nbTLLVs;
  UInt32 presenceFlags;
} SKDServerSPCDataV1Parser;

typedef struct {
  UInt32 *versions;
  UInt32 nbVersions;
} SKDServerSupportedVerions;

typedef struct SKDServerClientRefTime_ {
  UInt32 date; /* first play back date expressed in seconds elapsed since
                  1970-01-01T00:00:00Z */
  UInt32 playback;
  UInt64 playbackId;
} SKDServerClientRefTime;

typedef struct {
  UInt8 antiReplaySeed[PS_AES128_KEY_SZ];
  UInt8 DAS_k[PS_AES128_KEY_SZ];
  UInt8 sk[PS_AES128_KEY_SZ];
  UInt8 hu[PS_V1_HASH_SZ];
  UInt8 r2[PS_V1_R2_SZ];
  UInt8 r1[PS_V1_R1_SZ];
  UInt8 sk_r1_integrity_tag[PS_V1_SKR1_INTEGRITY_SZ];
  UInt8 sk_r1_integrity[PS_V1_SKR1_INTEGRITY_SZ];
  UInt8 sk_r1[PS_V1_SKR1_SZ];
  UInt32 p_version_used;
  SKDServerSupportedVerions p_version_supported;
  SKDServerTLLV *returnTLLVs;
  UInt32 nbReturnTLLVs;
  SKDServerSPCDataV1Parser spcDataParser;
  SKDServerClientRefTime playInfo;

  UInt8 assetId[PS_V1_ASSET_ID_MAX_SZ];
  UInt32 assetIdSize;

  UInt32 whereToPlay;
} SKDServerSPCDataV1;

typedef struct {
  UInt32 version;
  UInt8 aesKeyIV[PS_AES128_IV_SZ];
  UInt8 aesWrappedKey[PS_V1_WRAPPED_KEY_SZ];
  UInt8 certificateHash[PS_V1_HASH_SZ];
  UInt8 *spcDecryptedData;
  UInt32 spcDataSize;
  UInt32 spcDataOffset;
  SKDServerSPCDataV1 spcData;
  SKDServerParser parser;
} SKDServerSPCContainerV1;

typedef struct {
  UInt32 leaseDuration;
  UInt32 rentalDuration;
  UInt32 keyType;
  UInt32 reserved;
} SKDServerKeyDuration;

typedef enum SKDClientRefTimePlayback_ {
  kCurrentlyPlayingCKNotRequired = 0xa5d6739e,
  kFirstPlaybackCKRequired = 0xf4dee5a2,
  kCurrentlyPlayingCKRequired = 0x4f834330,
  kPlaybackSessionStopped = 0x5991bf20,
} SKDClientRefTimePlayback;

typedef struct {
  UInt32 tag_ck_TotalSize;
  UInt8 ck[PS_AES128_KEY_SZ];
  UInt8 iv[PS_AES128_IV_SZ];
  UInt32 tag_r1_TotalSize;
  UInt8 r1[PS_V1_R1_SZ];
  SKDServerParser parser;
  SKDServerKeyDuration keyDuration;

  UInt8 orig_ck[PS_AES128_KEY_SZ];
} SKDServerCKCDataV1;

typedef struct {
  UInt32 version;
  UInt32 regenreate_aes_iv;
  UInt8 aesKeyIV[PS_AES128_IV_SZ];
  UInt8 *ckcDataPtr;
  UInt32 ckcDataSize;
  SKDServerCKCDataV1 ckcData;
  SKDServerParser parser;
} SKDServerCKCContainerV1;

typedef struct {
  SKDServerSPCContainerV1 spcContainer;
  SKDServerCKCContainerV1 ckcContainer;
  const UInt8 *assetId;
} SKDServerCtxV1;

#endif
