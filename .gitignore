# Docker
.docker/
docker-compose.override.yml

# Logs
*.log
logs/

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Temporary files
*.tmp
*.temp
.cache/

# Test coverage
coverage/

# Environment variables
.env
.env.local
.env.*.local

# Local configuration
config/local/
*.local.json

# Docker volumes data
volumes/

# Backup files
*.bak
*.backup