var assert = require('assert');
var fairplay_test = require('../drm-plugin-fairplay.js');

var globalConfig = {
    ERROR: {
        PLUGIN_NOT_COMPATIBLE: 1,
        PLUGIN_NOT_PROCESSABLE: 2,
        NO_SUPPLY_KEY: 1000,
        NO_SUPPLY_IV: 1001,
        NO_SUPPLY_EXPIRETIME: 1002
    }
};
var fairplay_config = {
******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
    "server_certificate": "MIIE8zCCA9ugAwIBAgIINjfo+oC8kRwwDQYJKoZIhvcNAQEFBQAwfzELMAkGA1UEBhMCVVMxEzARBgNVBAoMCkFwcGxlIEluYy4xJjAkBgNVBAsMHUFwcGxlIENlcnRpZmljYXRpb24gQXV0aG9yaXR5MTMwMQYDVQQDDCpBcHBsZSBLZXkgU2VydmljZXMgQ2VydGlmaWNhdGlvbiBBdXRob3JpdHkwHhcNMTUwNzIxMDk0NzUxWhcNMTcwNzIxMDk0NzUxWjCBgDELMAkGA1UEBhMCVVMxIzAhBgNVBAoMGktLQk9YIFRlY2hub2xvZ2llcyBMaW1pdGVkMRMwEQYDVQQLDApBRFM4Nkc0MzdRMTcwNQYDVQQDDC5GYWlyUGxheSBTdHJlYW1pbmc6IEtLQk9YIFRlY2hub2xvZ2llcyBMaW1pdGVkMIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDDyjVfcj6U29rlL9Kk2myUNelZ9CEL4lCm0TCK1yI0sPK2oMeTtvSTwtGOZc5pbGodJhq5MxOd/6JUqRuh2ursFs/t+/5PazHrZxUVl96MTFwrWImCzHbnIxoTKlDgrN0WbaxOWhw0qRr6wnhA4Is5XB4Y/MgCHrWpheXLm+szcQIDAQABo4IB8zCCAe8wHQYDVR0OBBYEFI0peusa+omBTiwPyWR2E97XQVUpMAwGA1UdEwEB/wQCMAAwHwYDVR0jBBgwFoAUY+RHVMuFcVlGLIOszEQxZGcDLL4wgeIGA1UdIASB2jCB1zCB1AYJKoZIhvdjZAUBMIHGMIHDBggrBgEFBQcCAjCBtgyBs1JlbGlhbmNlIG9uIHRoaXMgY2VydGlmaWNhdGUgYnkgYW55IHBhcnR5IGFzc3VtZXMgYWNjZXB0YW5jZSBvZiB0aGUgdGhlbiBhcHBsaWNhYmxlIHN0YW5kYXJkIHRlcm1zIGFuZCBjb25kaXRpb25zIG9mIHVzZSwgY2VydGlmaWNhdGUgcG9saWN5IGFuZCBjZXJ0aWZpY2F0aW9uIHByYWN0aWNlIHN0YXRlbWVudHMuMDUGA1UdHwQuMCwwKqAooCaGJGh0dHA6Ly9jcmwuYXBwbGUuY29tL2tleXNlcnZpY2VzLmNybDAOBgNVHQ8BAf8EBAMCBSAwMAYLKoZIhvdjZAYNAQMBAf8EHgE3cWRicnlqcXFwbzZtOG40c3Q5Mmd2cDhoeDJjdzBBBgsqhkiG92NkBg0BBAEB/wQvAXNrejRpb2pzcXJzbWp6NWRrdmcxbnJjNGxuamxveHR6ZWd4cnRpYnJhZWo5d3cwDQYJKoZIhvcNAQEFBQADggEBAC4Ou68UP3f7/UoqqFHn/xUET8droABBFOZCRiP7wVw1i/RVhbD2ksdcZJSpIAPYVLnKkMYsCm49blKAl+LUTzpD8Op7c8fSh7TbPab7YXDYa9C/VvLliWvEYeHCfZcdGDY3QBSHT4Wcm1CcRZABeA7YXWGiyuCwLsfCNhJ2AAISi1Gf9ehXVtJgwT8EYEMPk2BVpUSWUmLrc8+6GHIoKeeeqnO2wnX2TNJ1czZ8TxgvjLFtpDTgFV0D9mCdCsabcCIC1Q8ctWBguhqAXuH/52PJDIstyJbKWhYmG9Du7YixOqqCFEU6WG2rKhrWWTsPJOIauqIc4nRaEdSotgyyCJ0=",
    "ask": "Z5ft4wdReVfgNbtSlvCJ9Q=="
};

var spc = 'AAAAAQAAAABpCLq4GRaoKNya7Wxhr0D2ZLt6Irbw0LT5Aqn1Rr6i0juYt6uQJAgBrTd0y3H/1ZsTdjW5ZpW0j9ymB6j1tZkH/MXaCH4oxORWWSm8/UsbnudRzOJZOAvKNIm2T0IWDVlIdnFgYOnpw1YR0r73FySvvwvD0kO/IyIIY0OYRrQP6bL9o7pWKqWF4z6IlqrZXXM62tilpYBjWZVbU6wz68s5eHiHpgAAEBCoE5LVNkvbYU3I6GzuuZM1gtrzJIY4nQJUYBJv9sdWgHWRhyQTeManEhPpEfLywtsOn9LOZCpatXDPz8+85QSOwmMnz5qk4lVQo+e4UGDIMXuUnjAOYtllNrjbGt6vlWBCn3C+CwC2acJbROGKgjmz76IhjKETLOMIh0HPsAfIKezRQEY46WKYZ2hD3PIXF+t1ILNBElnYsKPetsyR9Q4hKhrZdvzYYLJ6iXE8WZe5cFv8ZdzDo74MDvHFld33/3R+0LC7s9KtpWajK/mDtfc9ZkEvrbU9pQHdV1A7L2hxyOd7Q8VqfBS9Kl5dZVdFKm/oeCrupBQGRopTqGTpTMAp0bcCohGcOE5Qy80/aYPOE0crKUhskiA9lYfMeiMPijJjBDDZuOCqU0w8ZOJpxwluEzW3aINmRynpjdqqczkdsHIgciHlDmtf9O+2ikEmQCpPZkVPIXlJVahJuEfIxhNzFBk3UdDk5zytYAAevvVjpRNoZi4tnF49OY4uIlMKkJeaLVp+KAF0Lf9K6tf0RCJQeoXikfCyZ7zusrYNasq1SA/26nkE6nG7XU0CaQUvJNTH4QY2V6lb4OBuLJZBtEhAkOx7B0QXlc0gnDue6qKjkzX3MXgpPU360i61Wb6GPqEddB5OgRo9YzDqCX9UCxG2x8HIsbZ1rQT/6l6uGJ2n71viqV6pfolXl6/hTB0ja6xXp2aggwbj5/UWrqu9tialeCgxlSiJSLgjI53cd2ROFbdrqiaPEMWw/j5Jx2jIV26O1gxjKdKS5d6R792t27eYnn7zsF4IObcGpYzUzsyHW4xIiyN1Z2dWcXNYrwXohkj2eExp5lwoVi/w+yJunsocl0neJD5TFF3hkIcnDfz5N0JzVMR8U1eUu1C77qF5zedPzoOrBCgEsXNXcIL+MpcWWnSC4Mb0eo5aBxeXrKVk4+shHQLbipBZ2PkSDAvyIsf9+6WU0Lks1ZExpzGixiK+Zrlyc0DrrUAzmyhn1wqPSbmtNOFdTTE6nw4kE6Za5tBEj+QEnf2Dg8SRr2pigV+Z8AGdNg6BuiFaouHkmjpXWSgZx/eFPSKskIitT6SJ0JpVA8AWdZKSmoguKe4mFuazujlaJmK4LjncMe2DDXgewlLN1sGsFrMkbWZ+ex1ScEMSvKFi6jGzB5TjNhBSkujk3CdHIRAp//hHm+lF25OWlv7R13IQ2DDmYurmpe1MmJL9wYb8ftM++iBvlzNSqv0u4zU8RDbzrf3EaikzQ7SuNSaj7/XxjXY8MDKHq/tAyAPN6LUqvu8liRy4Ox0I/nd+k55KJawaNldLCigAp/35d3BkWWlu3FaJWC57ob6xnd9VYFtA1QOvyYh4XiVa9sJNQgVgVgbw6whGmpZ5Ko+RMwxzbK5gb4OAPT7ZKAiZnBsD0xRufEXhm8X+Gr+VAdSsktUl+1w4azJkwok2ayHE2QzJJThCv3UNEd/TccKNmJChQs8zF+Bdh1W6kg/bXDYCK3qKTH8ZpiI33TPNDJBG1HhdxdyeFM3o39D5ukZ2mNlbSxOg8UCXPR8r2OH7dmRj4VQ+JLqGGZreY3MLrK86qW+eMfFM1hYfD44Hs+rRZ3aYrpAQbvTAGpNDFQicjhImkfRhIT+DHIqsW0SMtpLBvkNQhMIopuc0TByRNw7W2fMNaGZX6aZQuspSL5A3A+qKcKw62WzgCSQrWo+bU1IBVgurZCPWCD5OR5gJ+nuKP6i/gVB3kEyjyf4G4KOzgxY36z0+giofW5rxmCzztDW/eQsqteat9WfMqd8G7LS+Rc8xurwK/ryveVIP6evpa9EFLde1Fjyq7tBsQ5SVVOdWyqsPHzAYgMZDJiIoEh9iNs8oN6xm8INfviTrcR76hyV3Gk4QPTKDyqCWA7lePRD96De7GY5/gSDtlZAG9frQhvuYavJjIRnvK0XGK/9Aul/VKom5iOGhMvKpMCpcLEE5w07APMzdDMt8dNerEbnDsoVeLL9isAuQWQrK+6lxBywhje7E4Y4BH09WoMLm098t2GGW8wztYVehVo+90j3HuzKyi6/VpsqU6Y0Ibd4IPI63T5PDFIekZS9asy2EvmGahqjggu6mRh0td8oitf7KW74fFxlYnXtd9p4+bbsM41MV029EwYHRe6wi8qdK5JrwPFo8skyiWzuDR/Hk46RXPHDAlqG0uqUEd/9NZEo+nb6aZPTW2yFZuqsl/S4WyJJElPF7+DW0fnHEFQ/AOLOZtX5kds6gahtHCjikDB8uq+hrodfS7xNU2kGNOGxNyRlRPFInmtulN0kC6nyz0YcSTGa83Kr8z/532s1lPFMR564b9HJmkmtijUaAk9OxU1vN+mOj6EoIxbmm4yEqj0NXZLCxJv7zmTduElQoz8mLSRXA61vyoawS0eJ82RPSHLWk0ih4gdXNMC/vWMbvsQAUmDForiLMzQGp3Vlb7+z1VuDpB44jiAUEZJml1ZJFc/a75rT1vVcPWVW+gF6+Fnbyk3exlQnd/RUWYYbMfzLmN+wzaNN79g1OvTWgnocgOGuM1yMTeteiZsxtgeMmzxXTrk5QV/BK7IUA40ZBdtsiwAv3oTh0FinR0bqqNXkIRexsbinZMqoIC/rRXEHDRQg1q8Z6iA01uSeAOFYA7BI7P9KZ1rwBYswYrupIJYKtvLvy/z22QkIXTi+Bw0MZ9TpJvPkHSeot5vGjN/19/2u7kC0Ut8ZdzygnNtWrU7B8IhbOtiLerEsvJwIJwarvxILhUZWlILOEnMIlF9YaKtvzKnrZ8WvV7OUwKFBPWbU/7YI5ID/OnW45W4A9IYpZQZj8iZU/ldRud5CHDMA4JBEyxWKNObeVVjFmJrfpMqLpvMc+yRBoHKRzfvQvsrMPWTY2vEitEBDW4I/ORcGKScFMGYXcw5QtETi2IRWID+eEunAquV0vX0I7AVqzhQ1K/WE/OqvWmCtOWk2ZM4m4qsulnMHTF6sVZ7gkBBPlpvblbAEsYUUcmiHCG9VeM0B+53dLgibbjDn1a449kV3VVKrNMEVVjxAL5sU+NAEOnqfglV4G9Hzc2UUEvQHuiqYszQrbiv4Jl6gbC2Oxro+KXN5X44HtvbUnUUONHRyxEG3xohuFBBCYsdRQXWW7XoIbc51Eed+ySV5hXcrvse1nUw78qmctVhvoNhfZUP/Z3Xf8VatH6nYxfrF0VX/TgFOc3px5ydoLQ8rLMGw7wYlIEs0cHLoWRe6xu55LLWg7CLMUEe0Ff681h4ULZSbni3rCqdUkvnx7e3JcLyIkbUdYHcRP/SFCf/Y9FS4iiV0flPQoDji0ip+m1yhv7AnOn0rODo1oCcXTl9EcdvJN30bXjkXP5zFgY8NgNmoyoTX/T2cnU+IzEO+/N23sf73TmowZ8H1y/2Fl2Uh91wmlfz46c7DDQGdGWB4XcNl3YbEPGt3W/GRB6gVBRi5Y6kNfZc2exRX53/HoAtdUdLKuEruE62G10EURKAAwkg/wUW+WDy0ShMwaJk/sl60NZZt4TRUp22ON3vSjGeII54L1VC1jrnq94weYaZMgIishj8sMDTDpBBCu4fE7372H+o7iUge8XklWe+GoonouRjM3PxFzCPnrdIKg+bJPQR9UULrg3unwXc1i6ct8MujkgtpsXDQDgJsGwGM/BqoyF5ndOUDpGfYu8zE3FPme8aeqGnnlBga1On+ndBKGuI1WMOwFqzhsxyq536uCES/zGByPrdKyMITQferWl/1OODJD0JLfRr6oAjrfspOvbMkmB2zNVw0lJi3dlYoX8tMHlfnzCZEr4g16SxhlZEMnP0CHWyIhWOtf8tNoGtU7vfh0SB8bFLv/0z87nNYe0afdYev+FaV2n9dJukXVFpw0ky5FtSmftUW/helmH9Lh1HJpWhuXBh6MAgfBAoV8jsNCDPY5pMIFw6cdJtgL84DQ7VXaMTY36rFG0ISNGUwjP/jLkaDmzkaLcTk0TslRDkxE4V8sk6yZNHDCX05oAyOHSSXHpq2Ojcda8YQuxxLRE0OfFTcYvYEwH4Hb/Rkk+Di63YrgjBI7KQ/dBvRNc2w27eDqdwA2FZ4lVCM5cAKMlLEKXk8Q2PZKxkR9iPSY5wIWzMuDrm3pWsayMbKGAk/M4oIjvFjBzFrycZpDJw5I8fCUetmo37Cfkpiv7AfCi8+PkmVsJ4hczuQZ6ixt6MIP35gw9FfkYXhubby1pZnj3maTddINn9CGbFzbq+Kazn0gn5bF746peaGMk7Tii31D0WY77gu1rXoyPN1c6aQchCMmhFxOz9AlcL0RHqg9WV5PdJu7BdQL9DJ3yuzEfxTfkJMkuPD/O0V9TMVeSM3pYI89pLdDmk1V2A/HZ0Ek8TYaLw9JjgI7xAQHegYBYiSHBv4ATHF1orUadHcpl31KV75CdQau8lUY7KDwyVhvXTjEgV4HuzlDjTLK60CX4kq8KnaXqcAL1rRs+9tqvvqgh/ntiF7USVV3AcSlw7fspqlpbAW7ajTssPGUXQs9BSU09V/WXqQP6gWxR+i5tVT0SVbm/BdiK8+d4C9tvnsxX4Hi9tOuFvhNx+6CjGBmzaK1Q1d31S9zNFfELi7qaOmbC3/7xswNftlRONngaQNStCOmK0/zOlg8BDzkilxwhbnxGKfoadABwGRQbr5JlTlCiSPJ/uBZ8SoLUAlF7b+WEXPSpvLwbTTL9D+ahz/3p3+WQJc7fu7ZG+v07ZBx+bdjSy+tLVExOMaR9tATcvnkHs/xMOrGH9dEUlwJTZBAvi8IIKHvmQGyWFCUHkM92QyynriUAPDrS68cPsyAFn/HnQ98GH/5T+FFto+tfIPaH70IGvFP4cuSLdE2Zz/zB0Wjh+/ORU2gL6gOr4p4++Y90mNdJPltMhHmOk6lOy5KkLVeyhuNlPtEg7w64FvzQsOrVoJWcBcNwXKuauuFekVTtmQG/QPJ31MdmZNGTgRHD/OFbESvon7LwgMC+zupGhoiEl7D91A1/HAS+Gszxx3iVufE1GxIf8J5/XojIv94qdiBpM4MLEZMDLsXkJSDdPNO2SzDwxh43Nc0LQrn8PpfUM9Qf6WOoDlG0PMtw2oFhEOf7L2cBWIpEc4UwIsjjFSRD25rQpCsENW5jAqoH+eLhoYwTZ+UEmfe45ZHHdgOACoWLdSJcNZ422HSMOaJmG5YN5T0NqmyuBBB1xgSAnQk2H+Ats4KM8RpMslwvn6MXJhH3DrBsk+4vhw7vRXpO775EtMufN6d8RCT2LRb7J9nOSRdkuS1wSgcWDP2eoA3YdUN0THYN2zNbX+aTxJCl8mHL1CUEEQ3ezdBJfMdY6zbi0aYBhA+lcR23j5xFARS2O95+Ut1HqKooRUdLGnuE7NJzRD6pK61SE/mJhobvIqwlrp46qe9ifnXqlEH1lQ4fEhGB8pNwd+x5cIhn5WjIQf7sadt95D+uYrlarygiqOAIYJ7pkQY0qO3SX/HQrcOlvbreSvv6vHZWMqUcfC7Wg==';
var ckc = 'AAAAAQAAAAAAEtaHABLWhwAS1ocAEtaHAAAC4A6JIKe/zTZvEVlRYPuytwa/MbDqu9szlc0a52Oadp3/TOZUSjiGbBtgzvIOtVHJOY0AWhfR+Xjk67xNRN4hPBC8Yh6jNBKn/EMl/hlwFcqPyrqnlwZlok4dxDWORzqnsVX+KgfNI3eNqxogTpHpJZ+nx6rd1Az3dWUkpdRd5rZDqvE2cLPGCOAoTUZs8Dl5mjaBsJ5trU3iPOo1WXi1HIwwKXsvGOUVIZpkpLlDYZNGhjjwOJ+0IpgiYzLaV73fcy0h39gizuUhXKp1Dg1KfwRKXZyk7wn+/5dd4Ix+B98PMe3HX3ghWGWCNtcXUV+52Hqz8Iev3kxgFQpA6YN64am9fWIXah1TOFLKfDxKw+R3QnfSuplSfXh0Dsgnx5in+wl/u4ywJ4FPM+Pc00Vnw/SYN/4pLSHLoUErN6IYfBpEbnH2WRcYXT2gzInCF1anfjt0OBYEA3S1f0NZDkIzCznm5joYT3+IXWwG+o18pUkKzPZFr9jX/3kvFqh9dO2h42b3bL7LfAfVtg0N80Q9qmpSyzCjny3ilNGHOjUTnVuGDNp8X48UFg00AK4N6scO/bq13r45+MJrRTu3Ip7IsVRTJMsqFAvrgRhezIJsrIfp5/371Saqf5upwjEOqqAIvauuXmNp7VRY9ToMTS211M67gB92gnciNP08SZu5bzRFFjPr7qpyV4Aw9HpdUtfSvfkApgU3n5fKpWDcslU2FY0cYTE6SFKTAUpWhOsVJrHzaT04rIXdvEh0xm1SthrgnAu4MLnxPAHb+RHNCUmAuJvx8dpodGFGcHSnhvyWaZZkS9mwsv+zwA1dPT77iS+2FZYSTSBJ7Af1AaMn9Uu530eN7QXfh8nPOfqvCMgCDY5uoTo1mThvCXCLO3zMbds5Mm8GC/H4zkbaq/2qmUcioAo+F3pihFxB4MbY4yjmXciaywrqFsetu0JtxhefpXhOAn446mm8DjomMqSZ5GXV3zI='
var ckc2 = 'AAAAAQAAAAAAEtaHABLWhwAS1ocAEtaHAAAC4A6JIKe/zTZvEVlRYPuytwa/MbDqu9szlc0a52Oadp3/TOZUSjiGbBtgzvIOtVHJOY0AWhfR+Xjk67xNRN4hPBC8Yh6jNBKn/EMl/hlwFcqPyrqnlwZlok4dxDWORzqnsVX+KgfNI3eNqxogTpHpJZ+nx6rd1Az3dWUkpdRd5rZDqvE2cLPGCOAoTUZs8Dl5mr3aJwQM5u/ReeDrpb3hkP1hLqxrK7pSiuWw6eqbZG/Zq+rYlSICblfbtqQhxb6yn/CwXtCdmS2pAxZRfkZT4qU4VJVjfsVdLco3/BVLEekfw8BppJRyTjNRgkPLvgvcCJiqBGcwkQ6oopOnCEBECsssO9V8bd/zCcuidH+2D51m4tYyas7DcKmrDwSFd2v5XgpIIXlc7DvcoLJioPxI1kCHTnfxnVeE6AvUhLsZNkYCX4s2ealvNeroIpkmrKbCu2CGRFqTBOQkDXxh8CUN82jOTpBTz+Q/cIZd5ybAGCGNkiA/bPEaJgiqK7cLNYNSgtZXNZa2pAcMPtsB+y9Om+k5vOT3bBdlqxAx8DOY6nTkdmgcCcuGipBXCE0qiPaIuwyCiB6rb6JLzbxd0dMq38jmuQNkDn8Cr3i55otk3DFq/7uS8toBtwGPdIvR4r3LuKfAcPJVG625QoIz1i7qTgzjMeYI3dJyTyYfeiUaDIVH/i3L2TEY8GMONHzRR/hTPJcmA4FKpYJPEXV5TSUtT6qfL3YfHSuqmLgzzijUV+fmgpixVtjDide4J/8IO2nLV263DnxFQ1fYqtjMkKbNYz1cd/AYxg1WrzlqsU8OpEqwEtVgqo13d/bitkRNN+mi+BF3x7ZR5XBvIkU+eaF8oU4Dnaq5I/rfYxMyy/OEJfR1+J36rWxNnkyXelBh+tyrpSejeL+4dmbhenrIwq8EaUNwwa06DJy/OoWTOwEuZ1qwCGfN/xpv3wLoH2mAtosjDQB2+bvmd7RAhsuI40AIpQc=';
var asset_id = '1234567890abcdef1234567890abcdef'
var cenc_key = '31323334353637383930313233343536';
var cenc_iv = '000102030405060708090a0b0c0d0e0f';

var expireDuration = 86401;

describe('FairPlay DRM Plugin', _ => {
    describe('Initialize', _ => {
        it('no General Config inputed', (done) => {
            assert.throws(_ => fairplay_test(), /No Configuration/);
            done();
        })

        it('no Plugin Config inputed', (done) => {
            assert.throws(_ => fairplay_test(globalConfig), /No Configuration/);
            done();
        })

        it('no Fairplay Private key', (done) => {
            assert.throws(_ => fairplay_test(globalConfig, { }), /No private key/);
            done();
        })

        it('no Fairplay DASk', (done) => {
            assert.throws(_ => fairplay_test(globalConfig, { private_key: fairplay_config.private_key }), /No DASk defined/);
            done();
        })

        it('no Server Certificate', (done) => {
            assert.throws(_ => fairplay_test(globalConfig, { private_key: fairplay_config.private_key, ask: fairplay_config.ask }), /No Server Certificate/);
            done();
        })

        it('initialize Ok', (done) => {
            assert.doesNotThrow(_ => fairplay_test(globalConfig, fairplay_config));
            done();
        })
    });

    describe('Server Certificate', (done) => {
        it('Get Server Certificate', (done) => {
            assert.equal(fairplay_test.getServerCertificate().toString('base64'), fairplay_config.server_certificate);
            done();
        })
    });

    describe('Test License Request', _ => {
        it('License Request = empty', (done) => {
            fairplay_test.requestMatch(null, new Buffer(''), (err, obj) => {
                assert.equal(err, globalConfig.ERROR.PLUGIN_NOT_COMPATIBLE);
                done();
            })
        });

        it('License Request has Fairplay signature, but not valid Fairplay SPC (spc=...&asset_id=...)', (done) => {
            fairplay_test.requestMatch(null, new Buffer('spc=AA&asset_id=abc'), (err, obj) => {
                assert.equal(err, globalConfig.ERROR.PLUGIN_NOT_PROCESSABLE);
                done();
            })
        });

        it('License Request has Fairplay signature, but not valid Fairplay SPC (pure spc)', (done) => {
            fairplay_test.requestMatch(null, new Buffer([0,0,0,1]), (err, obj) => {
                assert.equal(err, globalConfig.ERROR.PLUGIN_NOT_PROCESSABLE);
                done();
            })
        });

        it('License Request uses querystring format', (done) => {
            fairplay_test.requestMatch(null, new Buffer('spc=' + spc), (err, obj) => {
                assert.equal(err, null);
                assert.equal(obj.keyId, 'test');
                done();
            })
        });

        it('License Request uses querystring format, and rewrite keyId (1234)', (done) => {
            fairplay_test.requestMatch(null, new Buffer('spc=' + spc + '&asset_id=1234'), (err, obj) => {
                assert.equal(err, null);
                assert.equal(obj.keyId, '1234');
                done();
            })
        });

        it('License Request uses pure SPC ', (done) => {
            fairplay_test.requestMatch(null, new Buffer(spc, 'base64'), (err, obj) => {
                assert.equal(err, null);
                assert.equal(obj.keyId, 'test');
                done();
            })
        });
    });

    describe('Test License Response', _ => {
        var licenseObject;
        var lastResult;

        beforeEach((done) => {
            licenseObject = fairplay_test.requestMatch(null, new Buffer('spc=' + spc + '&asset_id=1234'), (err, obj) => { licenseObject = obj; done() })
        })

        afterEach((done) => {
            fairplay_test.releaseLicenseObject(licenseObject);
            done();
        });

        it('no Key', (done) => {
            licenseObject.key = null;
            fairplay_test.onLicenseAcquire(licenseObject, (err, obj) => {
                assert.equal(err, globalConfig.ERROR.NO_SUPPLY_KEY);
                done();
            })
        })

        it('key length != 32', (done) => {
            licenseObject.key = '1234';
            fairplay_test.onLicenseAcquire(licenseObject, (err, obj) => {
                assert.equal(err, globalConfig.ERROR.NO_SUPPLY_KEY);
                done();
            })
        })

        it('no IV', (done) => {
            licenseObject.key = cenc_key;
            fairplay_test.onLicenseAcquire(licenseObject, (err, obj) => {
                assert.equal(err, globalConfig.ERROR.NO_SUPPLY_IV);
                done();
            })
        })

        it('IV length != 32', (done) => {
            licenseObject.key = cenc_key;
            licenseObject.IV = '1234';
            fairplay_test.onLicenseAcquire(licenseObject, (err, obj) => {
                assert.equal(err, globalConfig.ERROR.NO_SUPPLY_IV);
                done();
            })
        })

        it('No expire time', (done) => {
            licenseObject.key = cenc_key;
            licenseObject.IV = cenc_iv;
            fairplay_test.onLicenseAcquire(licenseObject, (err, obj) => {
                assert.equal(err, globalConfig.ERROR.NO_SUPPLY_EXPIRETIME);
                done();
            })
        })

        it('All okay, and got valid License Result', (done) => {
            licenseObject.key = cenc_key;
            licenseObject.IV = cenc_iv;
            licenseObject.expireTime = (new Date).getTime() / 1000 + expireDuration;

            // FOR TEST: NEVER CODE FIXED IN THE PRODUCTION ENVIRONMENT
            licenseObject.licenseObject.forceRandomNumber = 1234567;

            fairplay_test.onLicenseAcquire(licenseObject, (err, obj) => {
                var result = obj.toString();
                if(result == '<ckc>' + ckc2 + '</ckc>') result = '<ckc>' + ckc + '</ckc>';
                assert.equal(result, '<ckc>' + ckc + '</ckc>');
                done();
            })
        })

        it('All okay, and not the same RandomNumber', (done) => {
            licenseObject.key = cenc_key;
            licenseObject.IV = cenc_iv;
            licenseObject.expireTime = (new Date).getTime() / 1000 + expireDuration;

            // FOR TEST: NEVER CODE FIXED IN THE PRODUCTION ENVIRONMENT
            licenseObject.licenseObject.forceRandomNumber = 12345670;

            fairplay_test.onLicenseAcquire(licenseObject, (err, obj) => {
                var result = obj.toString();
                if(result == '<ckc>' + ckc2 + '</ckc>') result = '<ckc>' + ckc + '</ckc>';
                lastResult = result;
                assert.notEqual(result, '<ckc>' + ckc + '</ckc>');
                done();
            })
        })

        it('All okay, and let the internal number random', (done) => {
            licenseObject.key = cenc_key;
            licenseObject.IV = cenc_iv;
            licenseObject.expireTime = (new Date).getTime() / 1000 + expireDuration;

            licenseObject.licenseObject.forceRandomNumber = 0;

            fairplay_test.onLicenseAcquire(licenseObject, (err, obj) => {
                var result = obj.toString();
                if(result == '<ckc>' + ckc2 + '</ckc>') result = '<ckc>' + ckc + '</ckc>';
                assert.notEqual(result, lastResult);
                done();
            })
        })
    });
})
