var assert = require('assert');
var widevine_test = require('../drm-plugin-widevine.js');

const ERROR = {
    HTTP_SERVER_NOT_IMPLEMENT: -1,

    AUTH_ERROR: 1000,
    AUTH_INTERNAL_ERROR: 1001,

    KEY_SERVER_INTERNAL_SERVER_ERROR: 2000,
    KEY_SERVER_UNKNOWN_ERROR: 2001,

    NO_SUPPLY_EXPIRETIME: 3000,
    NO_SUPPLY_IV: 3001,
    NO_SUPPLY_KEY: 3002,

    NOT_ALLOW_PLAY_THE_CONTENT_KEY_ID: 4000,

    NOT_FIND_SUITABLE_DRM_SOLUTION: 5000,

    PLUGIN_NOT_COMPATIBLE: 5001,
    PLUGIN_NOT_PROCESSABLE: 5002,

    REQUEST_URL_ERROR: 6000,

    TOKEN_TYPE_INVALIDATE: 7000,
};

// widevine test provided by Google
var widevine_config = {
    "license_server": "https://license.uat.widevine.com/cenc/getlicense",
    "provider": {
        "name": "widevine_test",
        "key": "1ae8ccd0e7985cc0b6203a55855a1034afc252980e970ca90e5202689f947ab9",
        "iv": "d58ce954203b7c9a9a9d467f59839249"
    }
};
