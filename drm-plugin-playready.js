const querystring = require('querystring');
const http = require('http');
const crypto = require('crypto');
const url = require('url');
const xml2js = require('xml2js');

const util = require('util');

var pluginConfig = null;
var globalConfig = null;

var playready = module.exports = function(general_config, plugin_config) {
    globalConfig = general_config;
    pluginConfig = plugin_config;

    if(!general_config || !plugin_config) throw new Error('No Configuration');
    if(!plugin_config.license_server) throw new Error('No License Server');
};

/**requestMatch(request, postBody, callback):
 *
 * test the request match the DRM provided by the plugin or not
 *
 * error:
 *      PLUGIN_NOT_COMPATIBLE
 *      PLUGIN_NOT_PROCESSABLE
 *
 * return value:
 *     license object:  {
 *       licenseRequest request to license server
 *       licenseObject  internal license object to process (option)
 *       keyId          processed key ID, format: 16bytes in hex format
 *       needIV         should set IV to this object
 *       IvId           IV ID: 16bytes in hex format
 *
 *       key            real Key to this request
 *       IV             real IV to this request
 *       expireTime     expire time on unix time
 *     }
 */
playready.requestMatch = function(req, body, callback) {
    process.nextTick(_ => {
        // make sure the request is playready <?xml
        if(!(req.headers['soapaction'] == '"http://schemas.microsoft.com/DRM/2007/03/protocols/AcquireLicense"' &&
            body[0] == 0x3c && body[1] == 0x3f && body[2] == 0x78 && body[3] == 0x6d && body[4] == 0x6c && body[5] == 0x20)) {
            return callback(globalConfig.ERROR.PLUGIN_NOT_COMPATIBLE, null);
        }

        // parse license challage
        (new xml2js.Parser()).parseString(body.toString(), function(err, result) {
            if(err) {
                globalConfig.logger(globalConfig.LOG.ERROR,{event:"try_drm",plugin:"playready",failed:"XML parse failed"});
                return callback(globalConfig.ERROR.PLUGIN_NOT_COMPATIBLE, null);
            }

            try {
                var LA = result['soap:Envelope']['soap:Body'][0]['AcquireLicense'][0]['challenge'][0]['Challenge'][0]['LA'][0];

                if(LA['Version'] != 1 || !LA['ContentHeader'][0]['WRMHEADER']) {
                    globalConfig.logger(globalConfig.LOG.ERROR,{event:"try_drm",plugin:"playready",failed:"Not vaild playready request"});
                    return callback(globalConfig.ERROR.PLUGIN_NOT_PROCESSABLE, null);
                }

                var WRMHEADER = LA['ContentHeader'][0]['WRMHEADER'][0]['DATA'][0];

                if(WRMHEADER['DS_ID'][0] == null || WRMHEADER['DS_ID'][0] != pluginConfig.service_id) {
                    globalConfig.logger(globalConfig.LOG.ERROR,{event:"try_drm",plugin:"playready",failed:"DS_ID Check failed: " + WRMHEADER['DS_ID'][0]});
                    return callback(globalConfig.ERROR.PLUGIN_NOT_PROCESSABLE, null);
                }

                if(WRMHEADER['PROTECTINFO'][0]['KEYLEN'] != 16 || WRMHEADER['PROTECTINFO'][0]['ALGID'] != 'AESCTR') {
                    globalConfig.logger(globalConfig.LOG.ERROR,{event:"try_drm",plugin:"playready",failed:"Invalid PROTECTINFO"});
                    return callback(globalConfig.ERROR.PLUGIN_NOT_PROCESSABLE, null);
                }

                if(WRMHEADER['KID'][0] == null) {
                    globalConfig.logger(globalConfig.LOG.ERROR,{event:"try_drm",plugin:"playready",failed:"KEY ID Not found"});
                    return callback(globalConfig.ERROR.PLUGIN_NOT_PROCESSABLE, null);
                }

                // The GUID in Playready is different to others, reorder it
                var tmp, keyId = new Buffer(WRMHEADER['KID'][0], 'base64');
                tmp = keyId[0]; keyId[0] = keyId[3]; keyId[3] = tmp;
                tmp = keyId[1]; keyId[1] = keyId[2]; keyId[2] = tmp;
                tmp = keyId[4]; keyId[4] = keyId[5]; keyId[5] = tmp;
                tmp = keyId[6]; keyId[6] = keyId[7]; keyId[7] = tmp;

                return callback(null, {
                    licenseRequest: body,
                    keyId: keyId.toString('hex')
                });
            } catch(e) {
                globalConfig.logger(globalConfig.LOG.ERROR,{event:"try_drm",plugin:"playready",failed:e});
                return callback(globalConfig.ERROR.PLUGIN_NOT_COMPATIBLE, null);
            }
        });
    });
}

/*
 * return license
 */
playready.onLicenseAcquire = function(clientPlatform, licenseObject, callback) {
    process.nextTick(_ => {
        if(!licenseObject.key || licenseObject.key.length != 32) {
            globalConfig.logger(globalConfig.LOG.INFO,{event:"license_acquire",failed:"key format error"});
            return callback(globalConfig.ERROR.NO_SUPPLY_KEY, null);
        }
        if(licenseObject.expireTime === undefined) {
            globalConfig.logger(globalConfig.LOG.INFO,{event:"license_acquire",failed:"no expire time"});
            return callback(globalConfig.ERROR.NO_SUPPLY_EXPIRETIME, null);
        }

        var queryParam = {
            expire: parseInt(licenseObject.expireTime)
        };

        // TODO: Model check and set OPL

        globalConfig.logger(globalConfig.LOG.INFO,{event:"license_acquire",subevent:"build message request",message:queryParam});
        var license_server = url.parse(pluginConfig.license_server + '?' + querystring.stringify(queryParam));

        license_server.method = 'POST';
        // The key should not be appeared in the log
        license_server.headers = {
            'X-Key': licenseObject.key,
            'Content-Type': 'text/xml; charset: UTF-8',
            'SOAPAction': "\"http://schemas.microsoft.com/DRM/2007/03/protocols/AcquireLicense\""
        };

        http.request(license_server, function(response) {
            var body = [];
            response.on('data', function(chunk) {
                body.push(chunk);
            }).on('end', function() {
                var rep = Buffer.concat(body).toString('utf-8');
                // our playready server store the client certifcation information in the headers, record it
                globalConfig.logger(globalConfig.LOG.INFO,{event:"license_acquire",subevent:"response",headers:response.headers,client_platform:clientPlatform});
                if(response.statusCode == 200) {
                    return callback(null, [ rep, { 'Content-Type': 'text/xml; charset=utf-8' }] );
                } else {
                    return callback(rep, rep);
                }
            });
        }).end(licenseObject.licenseRequest);
    });
};

playready.releaseLicenseObject = function(licenseObject) {
};
