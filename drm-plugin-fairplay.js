const querystring = require('querystring');
var fairplay_imp = require('./build/Release/fairplaystreaming').FPS;
var serverCertificate = null;
var pluginConfig = null;
var globalConfig = null;

const OUTPUT_FORMAT = {
    RAW: 0,
    XML: 1
};

var fairplay = module.exports = function(general_config, plugin_config) {
    globalConfig = general_config;
    pluginConfig = plugin_config;

    if(!general_config || !plugin_config) throw new Error('No Configuration');
    if(!plugin_config.private_key) throw new Error('No private key');
    if(!plugin_config.ask) throw new Error('No DASk defined');
    if(!plugin_config.server_certificate) throw new Error('No Server Certificate');

    fairplay_imp.SetPrivateKey(plugin_config.private_key);
    fairplay_imp.SetASK(new Buffer(plugin_config.ask, 'base64'));
    serverCertificate = new Buffer(plugin_config.server_certificate, 'base64');
};

/**requestMatch(request, postBody, callback):
 *
 * test the request match the DRM provided by the plugin or not
 *
 * error:
 *      PLUGIN_NOT_COMPATIBLE
 *      PLUGIN_NOT_PROCESSABLE
 *
 * return value:
 *     license object:  {
 *       licenseRequest request to license server
 *       licenseObject  internal license object to process (option)
 *       keyId          processed key ID, format: 16bytes in hex format
 *       needIV         should set IV to this object
 *       IvId           IV ID: 16bytes in hex format
 *
 *       key            real Key to this request
 *       IV             real IV to this request
 *       expireTime     expire time on unix time
 *     }
 */
fairplay.requestMatch = function(req, body, callback) {
    process.nextTick(_ => {
        var spc = null;
        var asset_id = null;
        var license_format;
        if(body[0] == 0 && body[1] == 0 && body[2] == 0 && body[3] == 1) {                  // pure spc
            spc = body;
            license_format = OUTPUT_FORMAT.RAW;
        } else if(body[0] == 0x73 && body[1] == 0x70 && body[2] == 0x63 && body[3] == 0x3d && body[4] == 0x41 && body[5] == 0x41) {         // spc=AA...
            var qs = querystring.parse(body.toString());
            spc = new Buffer(qs.spc.replace(/ /g, '+'), 'base64');
            asset_id = qs.asset_id;
            license_format = OUTPUT_FORMAT.XML;
        } else {
            callback(globalConfig.ERROR.PLUGIN_NOT_COMPATIBLE, null);
            return;
        }

        try {
            var fpsdata = new fairplay_imp(spc);
            if(!asset_id) asset_id = fpsdata.assetId;

            var m = asset_id.split('+');

            callback(null, {
                licenseObject: fpsdata,
                licenseRequest: spc,
                keyId: m[0],
                IvId: m[1],
                needIV: true,            // should call twice key server to fetch key and IV
                format: license_format
            });
        } catch(e) {
            callback(globalConfig.ERROR.PLUGIN_NOT_PROCESSABLE, null);
        };
    });
}

fairplay.getServerCertificate = function() {
    return serverCertificate;
};

/*
 * return license
 */
fairplay.onLicenseAcquire = function(clientPlatform, licenseObject, callback) {
    process.nextTick(_ => {
        var fps_data = licenseObject.licenseObject;

        if(!licenseObject.key || licenseObject.key.length != 32) return callback(globalConfig.ERROR.NO_SUPPLY_KEY, null);
        if(!licenseObject.IV || licenseObject.IV.length != 32) return callback(globalConfig.ERROR.NO_SUPPLY_IV, null);

        fps_data.key = new Buffer(licenseObject.key, 'hex');
        fps_data.iv = new Buffer(licenseObject.IV, 'hex');

        if(!licenseObject.expireTime) return callback(globalConfig.ERROR.NO_SUPPLY_EXPIRETIME, null);
        fps_data.rentalDuration = Math.floor(licenseObject.expireTime - ((new Date).getTime() / 1000));

        switch(licenseObject.format) {
            case OUTPUT_FORMAT.RAW:
                return callback(null, fps_data.GenerateLicense());
            case OUTPUT_FORMAT.XML:
                return callback(null, new Buffer('<ckc>' + fps_data.GenerateLicense().toString('base64') + '</ckc>'));
            default:
                return callback(globalConfig.PLUGIN_NOT_PROCESSABLE, null);
        }
    });
};

fairplay.releaseLicenseObject = function(licenseObject) {
};
