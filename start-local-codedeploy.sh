#!/bin/bash

# AWS CodeDeploy 本地模擬環境啟動腳本

set -e

# 顏色定義
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

# 顯示標題
show_banner() {
    echo -e "${CYAN}"
    echo "╔════════════════════════════════════════════════════════════════╗"
    echo "║                  AWS CodeDeploy 本地模擬環境                  ║"
    echo "║                     Local Testing Environment                  ║"
    echo "╚════════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
    echo ""
}

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 設置文件權限
setup_permissions() {
    log_info "設置腳本執行權限..."
    
    chmod +x simulate-codedeploy.sh
    chmod +x scripts/*.sh
    chmod +x codedeploy/*.sh 2>/dev/null || log_warning "codedeploy 目錄可能不存在或無腳本文件"
    
    log_success "權限設置完成"
}

# 顯示項目信息
show_project_info() {
    echo -e "${CYAN}項目信息:${NC}"
    echo "  應用名稱: kktv-drm"
    echo "  目標環境: Ubuntu 14.04"
    echo "  Node.js 版本: 4.x"
    echo "  部署工具: Ansible + npm"
    echo ""
}

# 檢查先決條件
check_prerequisites() {
    log_info "檢查先決條件..."
    
    local missing_deps=()
    
    if ! command -v docker &> /dev/null; then
        missing_deps+=("Docker")
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        missing_deps+=("Docker Compose")
    fi
    
    if [ ${#missing_deps[@]} -ne 0 ]; then
        log_error "缺少必要的依賴:"
        for dep in "${missing_deps[@]}"; do
            echo "  - $dep"
        done
        echo ""
        echo "請安裝缺少的依賴後重新運行此腳本。"
        exit 1
    fi
    
    log_success "先決條件檢查通過"
}

# 顯示菜單
show_menu() {
    echo -e "${CYAN}可用操作:${NC}"
    echo ""
    echo "  1) 快速開始 (設置 + 部署)"
    echo "  2) 僅設置環境"
    echo "  3) 執行部署測試"
    echo "  4) 查看日誌"
    echo "  5) 運行完整測試"
    echo "  6) 清理環境"
    echo "  7) 進入容器 (交互模式)"
    echo "  8) 顯示幫助"
    echo "  0) 退出"
    echo ""
}

# 執行快速開始
quick_start() {
    log_info "執行快速開始..."
    
    ./scripts/setup.sh
    echo ""
    log_info "等待容器完全啟動..."
    sleep 10
    
    ./scripts/deploy.sh
    echo ""
    log_success "快速開始完成！"
    
    show_next_steps
}

# 顯示下一步操作
show_next_steps() {
    echo ""
    echo -e "${CYAN}下一步操作:${NC}"
    echo ""
    echo "查看部署狀態:"
    echo "  ./scripts/logs.sh --status"
    echo ""
    echo "查看部署日誌:"
    echo "  ./scripts/logs.sh"
    echo ""
    echo "測試應用:"
    echo "  docker-compose exec codedeploy-sim bash -c 'cd /kkcorp/kktv-drm && node server.js'"
    echo ""
    echo "進入容器調試:"
    echo "  docker-compose exec codedeploy-sim bash"
    echo ""
}

# 進入交互模式
enter_interactive_mode() {
    log_info "進入容器交互模式..."
    
    if ! docker-compose ps | grep -q "Up"; then
        log_warning "容器未運行，正在啟動..."
        ./scripts/setup.sh
        sleep 5
    fi
    
    echo ""
    log_info "進入容器 (輸入 'exit' 退出)"
    echo ""
    docker-compose exec codedeploy-sim bash
}

# 顯示幫助信息
show_help() {
    echo -e "${CYAN}AWS CodeDeploy 本地模擬環境${NC}"
    echo ""
    echo "這個工具允許您在本地環境中模擬 AWS CodeDeploy 的部署流程。"
    echo ""
    echo "主要功能:"
    echo "  • 模擬 AWS CodeDeploy 的部署生命週期"
    echo "  • 在 Ubuntu 14.04 容器中測試部署腳本"
    echo "  • 提供詳細的部署日誌和錯誤信息"
    echo "  • 支持多次部署測試和回滾"
    echo ""
    echo "文件結構:"
    echo "  • Dockerfile - Docker 容器配置"
    echo "  • docker-compose.yml - Docker Compose 配置"
    echo "  • simulate-codedeploy.sh - 核心模擬腳本"
    echo "  • scripts/ - 輔助腳本目錄"
    echo "  • codedeploy/ - CodeDeploy 部署腳本"
    echo ""
    echo "使用流程:"
    echo "  1. 執行快速開始設置環境"
    echo "  2. 運行部署測試驗證腳本"
    echo "  3. 查看日誌排除問題"
    echo "  4. 修改腳本重新測試"
    echo "  5. 部署到 AWS 生產環境"
    echo ""
}

# 主菜單循環
main_menu() {
    while true; do
        echo ""
        show_menu
        echo -n "請選擇操作 [1-8,0]: "
        read -r choice
        
        case $choice in
            1)
                quick_start
                ;;
            2)
                ./scripts/setup.sh
                ;;
            3)
                ./scripts/deploy.sh
                ;;
            4)
                ./scripts/logs.sh
                ;;
            5)
                ./scripts/test.sh
                ;;
            6)
                ./scripts/cleanup.sh
                ;;
            7)
                enter_interactive_mode
                ;;
            8)
                show_help
                ;;
            0)
                log_info "退出程序"
                exit 0
                ;;
            *)
                log_error "無效選項，請重新選擇"
                ;;
        esac
        
        echo ""
        echo "按 Enter 鍵繼續..."
        read -r
    done
}

# 主函數
main() {
    show_banner
    show_project_info
    check_prerequisites
    setup_permissions
    
    # 如果有命令行參數，直接執行對應操作
    case "$1" in
        --quick-start|--quick)
            quick_start
            exit 0
            ;;
        --setup)
            ./scripts/setup.sh
            exit 0
            ;;
        --deploy)
            ./scripts/deploy.sh
            exit 0
            ;;
        --test)
            ./scripts/test.sh
            exit 0
            ;;
        --logs)
            ./scripts/logs.sh
            exit 0
            ;;
        --cleanup)
            ./scripts/cleanup.sh
            exit 0
            ;;
        --help|-h)
            show_help
            exit 0
            ;;
        "")
            # 無參數時顯示菜單
            main_menu
            ;;
        *)
            echo "未知選項: $1"
            echo "使用 --help 查看可用選項"
            exit 1
            ;;
    esac
}

main "$@"